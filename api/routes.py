"""API路由

定义GWHW网关MCP服务的所有API端点。
"""

from typing import Optional
from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends, Request
from models import (
    BuildRequest, DeployRequest, BuildPluginRequest, DeployPluginRequest,
    BuildDeployRequest, BuildDeployPluginRequest, PcapTestRequest,
    TaskResponse, TaskListResponse, SuccessResponse,
    CredentialCreateRequest, CredentialUpdateRequest, CredentialResponse,
    CredentialListResponse, CredentialBatchImportRequest, CredentialBatchImportResponse,
    CredentialBackupData
)
from models.task_models import TaskStatus
from services.build_service import BuildService
from services.test_service import TestService
from services.credential_service import CredentialService
from utils.task_manager import TaskManager
from core.logging import get_logger
from config.settings import settings

logger = get_logger(__name__)

# 创建路由器
router = APIRouter()

# 依赖注入
async def get_task_manager(request: Request) -> TaskManager:
    """获取任务管理器实例"""
    return request.app.state.task_manager

async def get_build_service(request: Request) -> BuildService:
    """获取构建服务实例"""
    return request.app.state.build_service

async def get_test_service(request: Request) -> TestService:
    """获取测试服务实例"""
    return request.app.state.test_service

async def get_credential_service(request: Request) -> CredentialService:
    """获取凭据服务实例"""
    return request.app.state.credential_service


# 构建相关API
@router.post("/build", response_model=TaskResponse, summary="启动构建任务", tags=["🔨 构建与部署"])
async def start_build(
    request: BuildRequest,
    build_service: BuildService = Depends(get_build_service)
):
    """启动构建任务

    Args:
        request: 构建请求参数

    Returns:
        任务响应
    """
    try:
        return await build_service.start_build_task(
            remote_ip=request.remote_ip,
            ssh_user=request.ssh_user,
            container_name=request.container_name
        )
    except Exception as e:
        logger.error(f"启动构建任务失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/deploy", response_model=TaskResponse, summary="启动部署任务", tags=["🔨 构建与部署"])
async def start_deploy(
    request: DeployRequest,
    build_service: BuildService = Depends(get_build_service)
):
    """启动部署任务

    Args:
        request: 部署请求参数

    Returns:
        任务响应
    """
    try:
        return await build_service.start_deploy_task(
            remote_ip=request.remote_ip,
            ssh_user=request.ssh_user,
            container_name=request.container_name
        )
    except Exception as e:
        logger.error(f"启动部署任务失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/build-plugin", response_model=TaskResponse, summary="启动插件编译任务", tags=["🔨 构建与部署"])
async def start_build_plugin(
    request: BuildPluginRequest,
    build_service: BuildService = Depends(get_build_service)
):
    """启动插件编译任务

    Args:
        request: 插件编译请求参数

    Returns:
        任务响应
    """
    try:
        return await build_service.start_build_plugin_task(
            plugin_name=request.plugin_name,
            remote_ip=request.remote_ip,
            ssh_user=request.ssh_user,
            container_name=request.container_name
        )
    except Exception as e:
        logger.error(f"启动插件编译任务失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/deploy-plugin", response_model=TaskResponse, summary="启动插件部署任务", tags=["🔨 构建与部署"])
async def start_plugin_deploy(
    request: DeployPluginRequest,
    build_service: BuildService = Depends(get_build_service)
):
    """启动插件部署任务

    Args:
        request: 插件部署请求参数

    Returns:
        任务响应
    """
    try:
        return await build_service.start_plugin_deploy_task(
            plugin_name=request.plugin_name,
            remote_ip=request.remote_ip,
            ssh_user=request.ssh_user,
            container_name=request.container_name
        )
    except Exception as e:
        logger.error(f"启动插件部署任务失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/build-deploy", response_model=TaskResponse, summary="启动构建部署统一任务", tags=["🔨 构建与部署"])
async def start_build_deploy(
    request: BuildDeployRequest,
    build_service: BuildService = Depends(get_build_service)
):
    """启动构建部署统一任务

    Args:
        request: 构建部署请求参数

    Returns:
        任务响应
    """
    try:
        return await build_service.start_build_deploy_task(
            build_remote_ip=request.build_remote_ip,
            build_ssh_user=request.build_ssh_user,
            container_name=request.container_name,
            deploy_remote_ip=request.deploy_remote_ip,
            deploy_ssh_user=request.deploy_ssh_user
        )
    except Exception as e:
        logger.error(f"启动构建部署统一任务失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/build-deploy-plugin", response_model=TaskResponse, summary="启动插件构建部署统一任务", tags=["🔨 构建与部署"])
async def start_build_deploy_plugin(
    request: BuildDeployPluginRequest,
    build_service: BuildService = Depends(get_build_service)
):
    """启动插件构建部署统一任务

    Args:
        request: 插件构建部署请求参数

    Returns:
        任务响应
    """
    try:
        return await build_service.start_build_deploy_plugin_task(
            plugin_name=request.plugin_name,
            build_remote_ip=request.build_remote_ip,
            build_ssh_user=request.build_ssh_user,
            container_name=request.container_name,
            deploy_remote_ip=request.deploy_remote_ip,
            deploy_ssh_user=request.deploy_ssh_user
        )
    except Exception as e:
        logger.error(f"启动插件构建部署统一任务失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 测试相关API
@router.post("/test/pcap", response_model=TaskResponse, summary="启动pcap文件测试", tags=["🧪 测试管理"])
async def start_pcap_test(
    request: PcapTestRequest,
    test_service: TestService = Depends(get_test_service)
):
    """启动pcap文件测试

    Args:
        request: PCAP测试请求参数

    Returns:
        任务响应
    """
    try:
        return await test_service.start_pcap_test(
            pcap_file_path=request.pcap_file_path,
            protocol_name=request.protocol_name,
            remote_ip=request.remote_ip,
            ssh_user=request.ssh_user
        )
    except Exception as e:
        logger.error(f"启动pcap测试失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/test/pcap-upload", response_model=TaskResponse, summary="启动pcap文件上传测试", tags=["🧪 测试管理"])
async def start_pcap_upload_test(
    file: UploadFile = File(..., description="pcap文件"),
    protocol_name: str = Form(..., description="测试协议名称"),
    remote_ip: Optional[str] = Form("", description="远程服务器IP"),
    ssh_user: Optional[str] = Form("", description="SSH用户名"),
    test_service: TestService = Depends(get_test_service)
):
    """启动pcap文件上传测试

    Args:
        file: 上传的pcap文件
        protocol_name: 测试协议名称
        remote_ip: 远程服务器IP（可选，使用默认值）
        ssh_user: SSH用户名（可选，使用默认值）

    Returns:
        任务响应
    """
    try:
        # 验证文件类型
        if not file.filename.endswith('.pcap'):
            raise HTTPException(status_code=400, detail="只支持.pcap文件")
        
        # 处理空字符串参数，转换为None以使用默认值
        remote_ip = remote_ip if remote_ip and remote_ip.strip() else None
        ssh_user = ssh_user if ssh_user and ssh_user.strip() else None
        
        # 读取文件内容
        file_content = await file.read()
        
        return await test_service.start_pcap_upload_test(
            file_content=file_content,
            filename=file.filename,
            protocol_name=protocol_name,
            remote_ip=remote_ip,
            ssh_user=ssh_user
        )
    except Exception as e:
        logger.error(f"启动pcap上传测试失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/test/pcap-upload-replay", response_model=TaskResponse, summary="启动pcap文件回放测试", tags=["🧪 测试管理"])
async def start_pcap_upload_replay_test(
    file: UploadFile = File(..., description="pcap文件"),
    protocol_name: str = Form(..., description="测试协议名称"),
    remote_ip: Optional[str] = Form("", description="远程服务器IP"),
    ssh_user: Optional[str] = Form("", description="SSH用户名"),
    eth_name: Optional[str] = Form("lo", description="网口名称"),
    test_service: TestService = Depends(get_test_service)
):
    """启动pcap文件回放测试
    
    Args:
        file: 上传的pcap文件
        remote_ip: 远程服务器IP（可选，使用默认值）
        ssh_user: SSH用户名（可选，使用默认值）
        eth_name: 网口名称（可选，默认为lo）
        wait_seconds: 等待处理时间（可选，使用默认值）
        log_lines: 日志输出行数（可选，使用默认值）
        
    Returns:
        任务响应
    """
    try:
        # 验证文件类型
        if not file.filename.endswith('.pcap'):
            raise HTTPException(status_code=400, detail="只支持.pcap文件")
        
        # 处理空字符串参数，转换为None以使用默认值
        remote_ip = remote_ip if remote_ip and remote_ip.strip() else None
        ssh_user = ssh_user if ssh_user and ssh_user.strip() else None
        eth_name = eth_name if eth_name and eth_name.strip() else None
        
        # 读取文件内容
        file_content = await file.read()
        
        return await test_service.start_pcap_upload_replay_test(
            file_content=file_content,
            filename=file.filename,
            protocol_name=protocol_name,
            remote_ip=remote_ip,
            ssh_user=ssh_user,
            eth_name=eth_name
        )
    except Exception as e:
        logger.error(f"启动pcap回放测试失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 任务管理API
@router.get("/tasks", response_model=TaskListResponse, summary="获取任务列表", tags=["📊 任务管理"])
async def get_tasks(
    status: Optional[str] = None,
    limit: int = 50,
    task_manager: TaskManager = Depends(get_task_manager)
):
    """获取任务列表

    Args:
        status: 任务状态过滤（可选）
        limit: 返回任务数量限制

    Returns:
        任务列表响应
    """
    try:
        tasks = await task_manager.get_tasks(status=status, limit=limit)
        return TaskListResponse(
            tasks=tasks,
            total=len(tasks),
            message="获取任务列表成功"
        )
    except Exception as e:
        logger.error(f"获取任务列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/{task_id}", response_model=TaskResponse, summary="获取任务详情", tags=["📊 任务管理"])
async def get_task(
    task_id: str,
    task_manager: TaskManager = Depends(get_task_manager)
):
    """获取任务详情
    
    Args:
        task_id: 任务ID
        
    Returns:
        任务响应
    """
    try:
        task = await task_manager.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 构建响应，使用任务对象的实际属性
        response_data = {
            "task_id": task.task_id,
            "status": task.status,
            "message": task.message,
            "created_at": task.created_at
        }
        
        # 添加可选属性
        if hasattr(task, 'started_at') and task.started_at:
            response_data["started_at"] = task.started_at
        if hasattr(task, 'completed_at') and task.completed_at:
            response_data["completed_at"] = task.completed_at
        
        # 处理任务结果 - 区分构建任务和测试任务
        if hasattr(task, 'stdout') and task.stdout:
            # 构建任务结果
            response_data["result"] = {
                "stdout": task.stdout, 
                "stderr": task.stderr, 
                "return_code": task.return_code
            }
        elif hasattr(task, 'hw_log') and (task.hw_log or task.hw_err or task.json_events):
            # 测试任务结果
            response_data["result"] = {
                "hw_log": task.hw_log or "",
                "hw_err": task.hw_err or "",
                "json_events": task.json_events or []
            }
        
        # 如果任务成功完成，根据配置决定是否自动删除任务数据
        if task.status == TaskStatus.SUCCESS and settings.auto_delete_completed_tasks:
            try:
                await task_manager.delete_task(task_id)
                logger.info(f"任务 {task_id} 结果已获取，根据配置自动删除任务数据")
            except Exception as delete_error:
                # 删除失败不影响结果返回，只记录日志
                logger.warning(f"自动删除任务 {task_id} 失败: {delete_error}")
        
        return TaskResponse(**response_data)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务详情失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/tasks/{task_id}", response_model=SuccessResponse, summary="删除任务", tags=["📊 任务管理"])
async def delete_task(
    task_id: str,
    task_manager: TaskManager = Depends(get_task_manager)
):
    """删除任务

    Args:
        task_id: 任务ID

    Returns:
        成功响应
    """
    try:
        success = await task_manager.delete_task(task_id)
        if not success:
            raise HTTPException(status_code=404, detail="任务不存在")

        return SuccessResponse(message="任务删除成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除任务失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 凭据管理API
@router.post("/credentials", response_model=CredentialResponse, summary="创建服务器凭据", tags=["🔐 凭据管理"])
async def create_credential(
    request: CredentialCreateRequest,
    credential_service: CredentialService = Depends(get_credential_service)
):
    """创建服务器凭据

    Args:
        request: 凭据创建请求

    Returns:
        创建的凭据信息（不包含密码）
    """
    try:
        logger.info(f"创建服务器凭据: {request.server_ip}")
        return await credential_service.create_credential(request)
    except Exception as e:
        logger.error(f"创建凭据失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/credentials", response_model=CredentialListResponse, summary="获取凭据列表", tags=["🔐 凭据管理"])
async def list_credentials(
    credential_service: CredentialService = Depends(get_credential_service)
):
    """获取所有服务器凭据列表

    Returns:
        凭据列表（不包含密码）
    """
    try:
        logger.info("获取凭据列表")
        return await credential_service.list_credentials()
    except Exception as e:
        logger.error(f"获取凭据列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/credentials/{server_ip}", response_model=CredentialResponse, summary="获取服务器凭据", tags=["🔐 凭据管理"])
async def get_credential(
    server_ip: str,
    credential_service: CredentialService = Depends(get_credential_service)
):
    """获取指定服务器的凭据信息

    Args:
        server_ip: 服务器IP地址

    Returns:
        凭据信息（不包含密码）
    """
    try:
        logger.info(f"获取服务器凭据: {server_ip}")
        credential = await credential_service.get_credential(server_ip, include_password=False)
        if not credential:
            raise HTTPException(status_code=404, detail=f"服务器凭据未找到: {server_ip}")

        return CredentialResponse(
            server_ip=credential.server_ip,
            ssh_user=credential.ssh_user,
            description=credential.description,
            created_at=credential.created_at,
            updated_at=credential.updated_at
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取凭据失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/credentials/{server_ip}", response_model=CredentialResponse, summary="更新服务器凭据", tags=["🔐 凭据管理"])
async def update_credential(
    server_ip: str,
    request: CredentialUpdateRequest,
    credential_service: CredentialService = Depends(get_credential_service)
):
    """更新指定服务器的凭据信息

    Args:
        server_ip: 服务器IP地址
        request: 凭据更新请求

    Returns:
        更新后的凭据信息（不包含密码）
    """
    try:
        logger.info(f"更新服务器凭据: {server_ip}")
        return await credential_service.update_credential(server_ip, request)
    except Exception as e:
        logger.error(f"更新凭据失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/credentials/{server_ip}", response_model=SuccessResponse, summary="删除服务器凭据", tags=["🔐 凭据管理"])
async def delete_credential(
    server_ip: str,
    credential_service: CredentialService = Depends(get_credential_service)
):
    """删除指定服务器的凭据

    Args:
        server_ip: 服务器IP地址

    Returns:
        删除结果
    """
    try:
        logger.info(f"删除服务器凭据: {server_ip}")
        success = await credential_service.delete_credential(server_ip)
        if success:
            return SuccessResponse(
                success=True,
                message=f"成功删除服务器凭据: {server_ip}"
            )
        else:
            raise HTTPException(status_code=500, detail="删除凭据失败")
    except Exception as e:
        logger.error(f"删除凭据失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/credentials/batch-import", response_model=CredentialBatchImportResponse, summary="批量导入凭据", tags=["🔐 凭据管理"])
async def batch_import_credentials(
    request: CredentialBatchImportRequest,
    credential_service: CredentialService = Depends(get_credential_service)
):
    """批量导入服务器凭据

    Args:
        request: 批量导入请求

    Returns:
        导入结果
    """
    try:
        logger.info(f"批量导入凭据，数量: {len(request.credentials)}")
        return await credential_service.batch_import_credentials(request)
    except Exception as e:
        logger.error(f"批量导入凭据失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/credentials/backup", response_model=CredentialBackupData, summary="备份所有凭据", tags=["🔐 凭据管理"])
async def backup_credentials(
    credential_service: CredentialService = Depends(get_credential_service)
):
    """备份所有服务器凭据

    Returns:
        备份数据
    """
    try:
        logger.info("备份所有凭据")
        return await credential_service.backup_credentials()
    except Exception as e:
        logger.error(f"备份凭据失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/credentials/restore", response_model=CredentialBatchImportResponse, summary="从备份恢复凭据", tags=["🔐 凭据管理"])
async def restore_credentials(
    backup_data: CredentialBackupData,
    overwrite_existing: bool = False,
    credential_service: CredentialService = Depends(get_credential_service)
):
    """从备份数据恢复凭据

    Args:
        backup_data: 备份数据
        overwrite_existing: 是否覆盖已存在的凭据

    Returns:
        恢复结果
    """
    try:
        logger.info(f"从备份恢复凭据，数量: {len(backup_data.credentials)}")
        return await credential_service.restore_credentials(backup_data, overwrite_existing)
    except Exception as e:
        logger.error(f"恢复凭据失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health", response_model=SuccessResponse, summary="健康检查", tags=["🏥 系统监控"])
async def health_check():
    """健康检查

    Returns:
        健康状态响应
    """
    try:
        return SuccessResponse(
            message="服务运行正常",
            data={
                "status": "healthy",
                "version": "1.0.0"
            }
        )
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/cleanup", response_model=SuccessResponse, summary="清理已完成的任务", tags=["📊 任务管理"])
async def cleanup_completed_tasks(
    task_manager: TaskManager = Depends(get_task_manager)
):
    """清理已完成的任务

    Returns:
        成功响应
    """
    try:
        count = await task_manager.cleanup_completed_tasks()
        return SuccessResponse(
            message=f"清理完成，删除了 {count} 个已完成的任务"
        )
    except Exception as e:
        logger.error(f"清理任务失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
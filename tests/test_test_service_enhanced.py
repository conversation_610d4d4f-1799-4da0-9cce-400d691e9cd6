"""测试服务增强功能测试

测试新增的协议名称参数和优化后的日志获取功能。
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch

from services.test_service import TestService
from utils.task_manager import TaskManager
from models.task_models import TaskStatus, TestTask
from models.response_models import TaskResponse


class TestTestServiceEnhanced:
    """测试服务增强功能测试类"""

    @pytest.fixture
    def mock_task_manager(self):
        """创建模拟任务管理器"""
        return AsyncMock(spec=TaskManager)

    @pytest.fixture
    def mock_credential_service(self):
        """创建模拟凭据服务"""
        return AsyncMock()

    @pytest.fixture
    def test_service(self, mock_task_manager, mock_credential_service):
        """创建测试服务实例"""
        return TestService(mock_task_manager, mock_credential_service)

    @pytest.mark.asyncio
    async def test_start_pcap_test_with_protocol(self, test_service, mock_task_manager):
        """测试启动pcap测试任务（包含协议名称）"""
        # 模拟任务创建
        mock_task = TestTask(
            task_id="test-123",
            status=TaskStatus.PENDING,
            message="pcap测试任务已创建，正在后台执行",
            remote_ip="*************",
            ssh_user="root",
            wait_seconds=5,
            log_lines=100,
            pcap_file_path="/path/to/test.pcap",
            protocol_name="postgre"
        )
        mock_task_manager.create_test_task.return_value = mock_task

        # 调用方法
        response = await test_service.start_pcap_test(
            pcap_file_path="/path/to/test.pcap",
            protocol_name="postgre",
            remote_ip="*************",
            ssh_user="root"
        )

        # 验证结果
        assert isinstance(response, TaskResponse)
        assert response.task_id == "test-123"
        assert response.status == TaskStatus.PENDING

        # 验证任务创建调用
        mock_task_manager.create_test_task.assert_called_once()
        call_args = mock_task_manager.create_test_task.call_args
        assert call_args.kwargs["protocol_name"] == "postgre"
        assert call_args.kwargs["pcap_file_path"] == "/path/to/test.pcap"

    @pytest.mark.asyncio
    async def test_start_pcap_upload_test_with_protocol(self, test_service, mock_task_manager):
        """测试启动pcap上传测试任务（包含协议名称）"""
        # 模拟任务创建
        mock_task = TestTask(
            task_id="upload-123",
            status=TaskStatus.PENDING,
            message="pcap上传测试任务已创建，正在后台执行 (test.pcap)",
            remote_ip="*************",
            ssh_user="root",
            wait_seconds=5,
            log_lines=100,
            filename="test.pcap",
            protocol_name="mysql"
        )
        mock_task_manager.create_test_task.return_value = mock_task

        # 调用方法
        file_content = b"pcap file content"
        response = await test_service.start_pcap_upload_test(
            file_content=file_content,
            filename="test.pcap",
            protocol_name="mysql",
            remote_ip="*************",
            ssh_user="root"
        )

        # 验证结果
        assert isinstance(response, TaskResponse)
        assert response.task_id == "upload-123"
        assert response.status == TaskStatus.PENDING

        # 验证任务创建调用
        mock_task_manager.create_test_task.assert_called_once()
        call_args = mock_task_manager.create_test_task.call_args
        assert call_args.kwargs["protocol_name"] == "mysql"
        assert call_args.kwargs["filename"] == "test.pcap"

    @pytest.mark.asyncio
    async def test_start_pcap_upload_replay_test_with_protocol(self, test_service, mock_task_manager):
        """测试启动pcap上传回放测试任务（包含协议名称）"""
        # 模拟任务创建
        mock_task = TestTask(
            task_id="replay-123",
            status=TaskStatus.PENDING,
            message="pcap回放测试任务已创建，正在后台执行 (test.pcap)",
            remote_ip="*************",
            ssh_user="root",
            wait_seconds=5,
            log_lines=100,
            filename="test.pcap",
            protocol_name="redis",
            eth_name="eth0"
        )
        mock_task_manager.create_test_task.return_value = mock_task

        # 调用方法
        file_content = b"pcap file content"
        response = await test_service.start_pcap_upload_replay_test(
            file_content=file_content,
            filename="test.pcap",
            protocol_name="redis",
            remote_ip="*************",
            ssh_user="root",
            eth_name="eth0"
        )

        # 验证结果
        assert isinstance(response, TaskResponse)
        assert response.task_id == "replay-123"
        assert response.status == TaskStatus.PENDING

        # 验证任务创建调用
        mock_task_manager.create_test_task.assert_called_once()
        call_args = mock_task_manager.create_test_task.call_args
        assert call_args.kwargs["protocol_name"] == "redis"
        assert call_args.kwargs["filename"] == "test.pcap"
        assert call_args.kwargs["eth_name"] == "eth0"

    @pytest.mark.asyncio
    async def test_execute_pcap_test_with_protocol_logs(self, test_service, mock_task_manager):
        """测试执行pcap测试任务时的协议日志获取"""
        # 模拟任务数据
        mock_task = TestTask(
            task_id="test-123",
            status=TaskStatus.PENDING,
            message="测试任务",
            remote_ip="*************",
            ssh_user="root",
            wait_seconds=1,  # 缩短等待时间
            log_lines=100,
            pcap_file_path="/path/to/test.pcap",
            protocol_name="postgre"
        )
        mock_task_manager.get_task.return_value = mock_task

        # 模拟SSH服务方法
        with patch.object(test_service.ssh_service, 'file_exists', return_value=True), \
             patch.object(test_service.ssh_service, 'execute_command', return_value=("", "", 0)), \
             patch.object(test_service.ssh_service, 'get_test_logs', return_value={
                 "hw_log": "postgre session: 6\npostgre parser: 20",
                 "hw_err": "[ERROR] test error",
                 "json_events": [{"event": "test"}]
             }) as mock_get_logs:

            # 执行测试
            await test_service._execute_pcap_test("test-123")

            # 验证日志获取调用包含协议名称
            mock_get_logs.assert_called_once_with(
                "*************", "root", 100, "postgre"
            )

            # 验证任务状态更新
            assert mock_task_manager.update_task_status.call_count >= 2
            # 最后一次调用应该是成功状态
            last_call = mock_task_manager.update_task_status.call_args_list[-1]
            assert last_call[0][1] == TaskStatus.SUCCESS

    @pytest.mark.asyncio
    async def test_default_values_handling(self, test_service, mock_task_manager):
        """测试默认值处理"""
        # 模拟任务创建
        mock_task = TestTask(
            task_id="test-123",
            status=TaskStatus.PENDING,
            message="pcap测试任务已创建，正在后台执行",
            remote_ip="*************",  # 默认值
            ssh_user="root",  # 默认值
            wait_seconds=5,  # 默认值
            log_lines=100,  # 默认值
            pcap_file_path="/path/to/test.pcap",
            protocol_name="postgre"
        )
        mock_task_manager.create_test_task.return_value = mock_task

        # 调用方法时不提供可选参数
        response = await test_service.start_pcap_test(
            pcap_file_path="/path/to/test.pcap",
            protocol_name="postgre"
        )

        # 验证使用了默认值
        call_args = mock_task_manager.create_test_task.call_args
        assert call_args.kwargs["remote_ip"] == "*************"
        assert call_args.kwargs["ssh_user"] == "root"
        assert call_args.kwargs["wait_seconds"] == 5
        assert call_args.kwargs["log_lines"] == 100


class TestSSHServiceLogFiltering:
    """SSH服务日志过滤功能测试类"""

    @pytest.fixture
    def ssh_service(self):
        """创建SSH服务实例"""
        from services.ssh_service import SSHService
        return SSHService()

    @pytest.mark.asyncio
    async def test_get_test_logs_with_protocol_filtering(self, ssh_service):
        """测试带协议过滤的日志获取"""
        with patch.object(ssh_service, 'execute_command') as mock_execute, \
             patch('utils.kafka_client.get_kafka_events', return_value=[{"event": "test"}]) as mock_kafka:

            # 模拟命令执行结果
            mock_execute.side_effect = [
                # hw.log 协议过滤命令
                ("postgre session: 6\npostgre parser: 20", "", 0),
                # hw.err 错误过滤命令
                ("[2025-07-25 17:13:24] [ERROR] test error\n[2025-07-25 17:13:25] [WARN] test warning", "", 0)
            ]

            # 调用方法
            result = await ssh_service.get_test_logs(
                host="*************",
                user="root",
                log_lines=100,
                protocol_name="postgre"
            )

            # 验证结果
            assert "hw_log" in result
            assert "hw_err" in result
            assert "json_events" in result

            assert "postgre session: 6" in result["hw_log"]
            assert "postgre parser: 20" in result["hw_log"]
            assert "[ERROR] test error" in result["hw_err"]
            assert "[WARN] test warning" in result["hw_err"]
            assert len(result["json_events"]) == 1

            # 验证命令调用
            assert mock_execute.call_count == 2

            # 验证hw.log命令包含协议过滤
            hw_log_call = mock_execute.call_args_list[0]
            hw_log_command = hw_log_call[0][2]  # 第三个参数是命令
            assert "grep -E" in hw_log_command
            assert "postgre" in hw_log_command

            # 验证hw.err命令包含错误级别过滤
            hw_err_call = mock_execute.call_args_list[1]
            hw_err_command = hw_err_call[0][2]
            assert "grep -E" in hw_err_command
            assert "ERROR|WARN" in hw_err_command

    @pytest.mark.asyncio
    async def test_get_test_logs_without_protocol(self, ssh_service):
        """测试不带协议过滤的日志获取"""
        with patch.object(ssh_service, 'execute_command') as mock_execute, \
             patch('utils.kafka_client.get_kafka_events', return_value=[]) as mock_kafka:

            # 模拟命令执行结果
            mock_execute.side_effect = [
                # hw.log 全部日志
                ("all log content", "", 0),
                # hw.err 错误过滤命令
                ("[ERROR] test error", "", 0)
            ]

            # 调用方法（不提供协议名称）
            result = await ssh_service.get_test_logs(
                host="*************",
                user="root",
                log_lines=100
            )

            # 验证结果
            assert result["hw_log"] == "all log content"
            assert result["hw_err"] == "[ERROR] test error"
            assert result["json_events"] == []

            # 验证hw.log命令不包含协议过滤
            hw_log_call = mock_execute.call_args_list[0]
            hw_log_command = hw_log_call[0][2]
            assert "grep -E" not in hw_log_command or "postgre" not in hw_log_command

    @pytest.mark.asyncio
    async def test_get_test_logs_kafka_integration(self, ssh_service):
        """测试Kafka集成"""
        with patch.object(ssh_service, 'execute_command') as mock_execute, \
             patch('utils.kafka_client.get_kafka_events') as mock_kafka:

            # 模拟Kafka事件
            mock_kafka.return_value = [
                {"event_type": "db_insert", "table": "users"},
                {"event_type": "db_update", "table": "orders"}
            ]

            # 模拟命令执行结果
            mock_execute.side_effect = [
                ("log content", "", 0),
                ("error content", "", 0)
            ]

            # 调用方法
            result = await ssh_service.get_test_logs(
                host="*************",
                user="root",
                log_lines=100,
                protocol_name="mysql"
            )

            # 验证Kafka调用
            mock_kafka.assert_called_once_with(timeout_seconds=10)

            # 验证结果包含Kafka事件
            assert len(result["json_events"]) == 2
            assert result["json_events"][0]["event_type"] == "db_insert"
            assert result["json_events"][1]["event_type"] == "db_update"

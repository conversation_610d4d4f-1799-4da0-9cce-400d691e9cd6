"""测试pcap回放服务

测试新的pcap回放测试接口功能。
"""

import pytest
from unittest.mock import AsyncMock, MagicMock
from services.test_service import TestService
from models.task_models import TestTask, TaskStatus
from models.response_models import TaskResponse
from config.settings import settings


class TestPcapReplayService:
    """测试pcap回放服务"""

    @pytest.fixture
    def mock_task_manager(self):
        """模拟任务管理器"""
        mock = AsyncMock()
        return mock

    @pytest.fixture
    def mock_ssh_service(self):
        """模拟SSH服务"""
        mock = AsyncMock()
        return mock

    @pytest.fixture
    def test_service(self, mock_task_manager, mock_ssh_service):
        """创建测试服务实例"""
        service = TestService(mock_task_manager, mock_ssh_service)
        # 确保使用模拟的SSH服务
        service.ssh_service = mock_ssh_service
        return service

    @pytest.mark.asyncio
    async def test_start_pcap_replay_test(self, test_service, mock_task_manager):
        """测试启动pcap回放测试任务"""
        # 模拟任务创建
        mock_task = TestTask(
            task_id="replay-123",
            status=TaskStatus.PENDING,
            message="pcap回放测试任务已创建，正在后台执行 (test.pcap)",
            remote_ip="*************",
            ssh_user="root",
            wait_seconds=5,
            log_lines=300,
            pcap_file_name="test.pcap",
            protocol_name="postgre",
            eth_name="lo"
        )
        mock_task_manager.create_test_task.return_value = mock_task

        # 调用方法
        response = await test_service.start_pcap_replay_test(
            pcap_file_name="test.pcap",
            protocol_name="postgre",
            remote_ip="*************",
            ssh_user="root",
            eth_name="lo"
        )

        # 验证结果
        assert response.task_id == "replay-123"
        assert response.status == TaskStatus.PENDING
        assert "test.pcap" in response.message

        # 验证任务管理器调用
        mock_task_manager.create_test_task.assert_called_once_with(
            remote_ip="*************",
            ssh_user="root",
            wait_seconds=settings.default_wait_seconds,
            log_lines=settings.default_log_lines,
            message="pcap回放测试任务已创建，正在后台执行 (test.pcap)",
            pcap_file_name="test.pcap",
            eth_name="lo",
            protocol_name="postgre"
        )

    @pytest.mark.asyncio
    async def test_start_pcap_replay_test_with_defaults(self, test_service, mock_task_manager):
        """测试使用默认值启动pcap回放测试任务"""
        # 模拟任务创建
        mock_task = TestTask(
            task_id="replay-456",
            status=TaskStatus.PENDING,
            message="pcap回放测试任务已创建，正在后台执行 (pgsql_test.pcap)",
            remote_ip=settings.default_remote_ip,
            ssh_user=settings.default_ssh_user,
            wait_seconds=settings.default_wait_seconds,
            log_lines=settings.default_log_lines,
            pcap_file_name="pgsql_test.pcap",
            protocol_name="postgre",
            eth_name=settings.default_eth_name
        )
        mock_task_manager.create_test_task.return_value = mock_task

        # 调用方法时只提供必需参数
        response = await test_service.start_pcap_replay_test(
            pcap_file_name="pgsql_test.pcap",
            protocol_name="postgre"
        )

        # 验证结果
        assert response.task_id == "replay-456"
        assert response.status == TaskStatus.PENDING
        assert "pgsql_test.pcap" in response.message

        # 验证任务管理器调用使用了默认值
        mock_task_manager.create_test_task.assert_called_once_with(
            remote_ip=settings.default_remote_ip,
            ssh_user=settings.default_ssh_user,
            wait_seconds=settings.default_wait_seconds,
            log_lines=settings.default_log_lines,
            message="pcap回放测试任务已创建，正在后台执行 (pgsql_test.pcap)",
            pcap_file_name="pgsql_test.pcap",
            eth_name=settings.default_eth_name,
            protocol_name="postgre"
        )

    @pytest.mark.asyncio
    async def test_pcap_file_path_construction(self, test_service, mock_task_manager, mock_ssh_service):
        """测试pcap文件路径构建逻辑"""
        # 模拟任务
        mock_task = TestTask(
            task_id="replay-789",
            status=TaskStatus.RUNNING,
            message="正在执行pcap回放测试",
            remote_ip="*************",
            ssh_user="root",
            wait_seconds=5,
            log_lines=300,
            pcap_file_name="mysql_test.pcap",
            protocol_name="mysql",
            eth_name="lo"
        )
        mock_task_manager.get_task.return_value = mock_task

        # 模拟SSH命令执行 - 设置side_effect来处理多次调用
        mock_ssh_service.execute_command.side_effect = [
            ("exists", "", 0),  # 文件存在检查
            ("tcpreplay output", "", 0),  # tcpreplay执行
        ]

        # 模拟日志获取
        mock_ssh_service.get_test_logs.return_value = {"hw_log": "test logs"}

        # 模拟任务状态更新
        mock_task_manager.update_task_status.return_value = True

        # 执行私有方法（通过反射）
        await test_service._execute_pcap_replay_existing_test("replay-789")

        # 验证文件路径构建
        expected_path = f"{settings.pcap_test_dir}mysql/mysql_test.pcap"
        
        # 验证SSH命令调用
        calls = mock_ssh_service.execute_command.call_args_list
        assert len(calls) >= 2
        
        # 检查文件存在性检查命令
        check_command = calls[0][0][2]  # 第三个参数是命令
        assert expected_path in check_command
        assert "test -f" in check_command
        
        # 检查tcpreplay命令
        replay_command = calls[1][0][2]  # 第三个参数是命令
        assert expected_path in replay_command
        assert "tcpreplay -i lo -M 100" in replay_command

"""Kafka客户端工具

提供Kafka消费者功能，用于获取JSON事件数据。
"""

import json
import asyncio
from typing import List, Dict, Any, Optional
from aiokafka import AIOKafkaConsumer
from aiokafka.errors import KafkaError

from config.settings import settings
from core.logging import get_logger

# 导入压缩库
try:
    import snappy
    SNAPPY_AVAILABLE = True
except ImportError:
    SNAPPY_AVAILABLE = False
    snappy = None

logger = get_logger(__name__)


class KafkaClient:
    """Kafka客户端"""

    def __init__(self):
        self.consumer: Optional[AIOKafkaConsumer] = None
        self.is_connected = False

    def _decompress_and_decode_value(self, value: bytes) -> Optional[str]:
        """解压并解码Kafka消息值

        Args:
            value: 原始字节数据（可能经过snappy压缩）

        Returns:
            解码后的字符串，如果解码失败则返回None
        """
        if not value:
            return None

        try:
            # 首先尝试snappy解压
            if SNAPPY_AVAILABLE:
                try:
                    # 尝试snappy解压
                    decompressed = snappy.decompress(value)
                    logger.debug("成功使用snappy解压Kafka消息")

                    # 解压后尝试UTF-8解码
                    decoded = decompressed.decode('utf-8')
                    return decoded

                except Exception as snappy_error:
                    # snappy解压失败，可能消息没有压缩，继续尝试直接解码
                    logger.debug(f"snappy解压失败，尝试直接解码: {snappy_error}")
            else:
                logger.warning("snappy库不可用，跳过解压步骤")

            # 如果snappy解压失败或不可用，尝试直接解码
            # 尝试多种编码方式
            encodings = ['utf-8', 'latin-1', 'ascii', 'utf-16', 'gb2312', 'gbk']

            for encoding in encodings:
                try:
                    decoded = value.decode(encoding)
                    # 验证解码结果是否包含可打印字符
                    if any(c.isprintable() or c.isspace() for c in decoded):
                        logger.debug(f"成功使用 {encoding} 编码解码Kafka消息")
                        return decoded
                except (UnicodeDecodeError, UnicodeError):
                    continue

            # 如果所有编码都失败，使用错误处理策略
            try:
                # 使用utf-8编码，忽略错误字符
                decoded = value.decode('utf-8', errors='ignore')
                logger.warning(f"Kafka消息包含无效字符，已忽略错误字符")
                return decoded
            except Exception:
                # 最后的降级方案：返回base64编码的字符串
                import base64
                encoded = base64.b64encode(value).decode('ascii')
                logger.warning(f"Kafka消息无法解码，返回base64编码: {encoded[:100]}...")
                return f"base64:{encoded}"

        except Exception as e:
            logger.error(f"解压和解码Kafka消息失败: {e}")
            return None

    async def connect(self) -> bool:
        """连接到Kafka服务器
        
        Returns:
            是否连接成功
        """
        try:
            self.consumer = AIOKafkaConsumer(
                settings.kafka_topic_db_event,
                bootstrap_servers=settings.kafka_bootstrap_servers,
                group_id=settings.kafka_consumer_group,
                auto_offset_reset='latest',  # 从最新消息开始消费
                enable_auto_commit=True,
                value_deserializer=self._decompress_and_decode_value
            )
            
            await self.consumer.start()
            self.is_connected = True
            logger.info(f"Kafka消费者连接成功: {settings.kafka_bootstrap_servers}")
            return True
            
        except Exception as e:
            logger.error(f"Kafka消费者连接失败: {e}")
            # 如果是压缩库缺失错误，提供详细的解决方案
            if "snappy" in str(e).lower() or "UnsupportedCodecError" in str(e):
                logger.error("Kafka snappy压缩库缺失，请安装: pip install python-snappy")
                logger.error("如果仍有问题，可能需要安装系统级snappy库:")
                logger.error("  Ubuntu/Debian: sudo apt-get install libsnappy-dev")
                logger.error("  CentOS/RHEL: sudo yum install snappy-devel")
                logger.error("  macOS: brew install snappy")
            self.is_connected = False
            return False

    async def disconnect(self):
        """断开Kafka连接"""
        if self.consumer:
            try:
                await self.consumer.stop()
                self.consumer = None
                self.is_connected = False
                logger.info("Kafka消费者连接已断开")
            except Exception as e:
                logger.error(f"断开Kafka连接失败: {e}")
                # 即使断开失败，也要重置状态
                self.consumer = None
                self.is_connected = False

    async def consume_events(self, timeout_seconds: int = 10, max_records: int = 100) -> List[Dict[str, Any]]:
        """消费JSON事件数据
        
        Args:
            timeout_seconds: 消费超时时间（秒）
            max_records: 最大记录数
            
        Returns:
            JSON事件列表
        """
        if not self.is_connected or not self.consumer:
            logger.warning("Kafka消费者未连接")
            return []

        events = []
        try:
            # 设置消费超时
            end_time = asyncio.get_event_loop().time() + timeout_seconds
            
            while len(events) < max_records and asyncio.get_event_loop().time() < end_time:
                try:
                    # 获取消息，设置较短的超时时间以便检查总超时
                    msg_map = await asyncio.wait_for(
                        self.consumer.getmany(timeout_ms=1000, max_records=max_records - len(events)),
                        timeout=1.0
                    )
                    
                    # 处理接收到的消息
                    for topic_partition, messages in msg_map.items():
                        for message in messages:
                            if message.value:
                                try:
                                    # message.value已经通过_decompress_and_decode_value处理过了
                                    if message.value.startswith("base64:"):
                                        # 处理base64编码的消息（无法解码的二进制数据）
                                        events.append({
                                            "error": "消息包含二进制数据，无法解码为文本",
                                            "encoding": "base64",
                                            "raw_content": message.value,
                                            "message_info": "原始消息可能使用了不支持的压缩格式或包含二进制数据"
                                        })
                                        logger.warning("接收到无法解码的Kafka消息，已转换为base64格式")
                                    else:
                                        # 尝试解析JSON
                                        event = json.loads(message.value)
                                        events.append(event)
                                        logger.debug(f"接收到Kafka事件: {event}")
                                except json.JSONDecodeError as e:
                                    logger.warning(f"JSON解析失败: {e}, 数据长度: {len(message.value)}")
                                    # 截断过长的内容以避免日志过大
                                    content_preview = message.value[:200] + "..." if len(message.value) > 200 else message.value
                                    events.append({
                                        "error": f"JSON解析失败: {str(e)}",
                                        "raw_content": content_preview,
                                        "content_length": len(message.value),
                                        "message_info": "消息已解压但不是有效的JSON格式"
                                    })
                                except Exception as e:
                                    logger.error(f"处理Kafka消息时发生未知错误: {e}")
                                    events.append({
                                        "error": f"消息处理失败: {str(e)}",
                                        "message_info": "消息处理过程中发生未知错误"
                                    })
                    
                    # 如果没有更多消息，稍等一下再试
                    if not msg_map:
                        await asyncio.sleep(0.1)
                        
                except asyncio.TimeoutError:
                    # 单次获取超时，继续尝试直到总超时
                    continue
                    
        except Exception as e:
            logger.error(f"消费Kafka事件失败: {e}")
            
        logger.info(f"从Kafka消费了 {len(events)} 个事件")
        return events

    async def get_recent_events(self, timeout_seconds: int = 10) -> List[Dict[str, Any]]:
        """获取最近的事件数据
        
        Args:
            timeout_seconds: 获取超时时间（秒）
            
        Returns:
            JSON事件列表
        """
        # 如果未连接，尝试连接
        if not self.is_connected:
            if not await self.connect():
                return []
        
        try:
            events = await self.consume_events(
                timeout_seconds=timeout_seconds,
                max_records=settings.kafka_max_poll_records
            )
            return events
            
        except Exception as e:
            logger.error(f"获取Kafka事件失败: {e}")
            return []

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.disconnect()


# 全局Kafka客户端实例
kafka_client = KafkaClient()


async def get_kafka_events(timeout_seconds: int = 10) -> List[Dict[str, Any]]:
    """获取Kafka事件的便捷函数（使用全局客户端）

    Args:
        timeout_seconds: 获取超时时间（秒）

    Returns:
        JSON事件列表
    """
    return await kafka_client.get_recent_events(timeout_seconds)


async def get_kafka_events_realtime(timeout_seconds: int = 10) -> List[Dict[str, Any]]:
    """获取实时Kafka事件的便捷函数（创建新的消费者连接）

    每次调用都会创建新的Kafka消费者连接，只消费当前时间点之后的新数据。
    适用于需要隔离数据的测试场景。

    Args:
        timeout_seconds: 获取超时时间（秒）

    Returns:
        JSON事件列表
    """
    # 创建新的Kafka客户端实例，确保数据隔离
    async with KafkaClient() as client:
        return await client.consume_events(
            timeout_seconds=timeout_seconds,
            max_records=settings.kafka_max_poll_records
        )


# 注册应用程序退出时的清理函数
import atexit
import asyncio

def cleanup_kafka_resources():
    """应用程序退出时清理Kafka资源"""
    if kafka_client.is_connected:
        logger.info("应用程序退出，正在清理Kafka资源...")
        try:
            # 创建新的事件循环来运行异步清理函数
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(kafka_client.disconnect())
            loop.close()
            logger.info("Kafka资源清理完成")
        except Exception as e:
            logger.error(f"Kafka资源清理失败: {e}")

# 注册退出清理函数
atexit.register(cleanup_kafka_resources)

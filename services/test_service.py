"""测试服务

提供GWHW网关的pcap文件测试功能。
"""

import asyncio
import os
import tempfile
from typing import Optional, TYPE_CHECKING

from models.task_models import TaskStatus
from models.response_models import TaskResponse
from core.logging import get_logger
from config.settings import settings
from utils.task_manager import TaskManager
from .ssh_service import SSHService

if TYPE_CHECKING:
    from .credential_service import CredentialService

logger = get_logger(__name__)


class TestService:
    """测试服务"""

    def __init__(self, task_manager: TaskManager, credential_service: Optional["CredentialService"] = None):
        self.task_manager = task_manager
        self.ssh_service = SSHService(credential_service)
    
    async def start_pcap_test(
        self,
        pcap_file_path: str,
        protocol_name: str,
        remote_ip: Optional[str] = None,
        ssh_user: Optional[str] = None,
        wait_seconds: Optional[int] = None,
        log_lines: Optional[int] = None
    ) -> TaskResponse:
        """启动pcap文件测试任务

        Args:
            pcap_file_path: pcap文件路径
            protocol_name: 测试协议名称
            remote_ip: 远程服务器IP
            ssh_user: SSH用户名
            wait_seconds: 等待处理时间
            log_lines: 日志输出行数

        Returns:
            任务响应
        """
        # 使用默认值，处理空字符串
        remote_ip = remote_ip if remote_ip and remote_ip.strip() else settings.default_remote_ip
        ssh_user = ssh_user if ssh_user and ssh_user.strip() else settings.default_ssh_user
        wait_seconds = wait_seconds or settings.default_wait_seconds
        log_lines = log_lines or settings.default_log_lines
        
        # 创建任务
        task = await self.task_manager.create_test_task(
            remote_ip=remote_ip,
            ssh_user=ssh_user,
            wait_seconds=wait_seconds,
            log_lines=log_lines,
            message="pcap测试任务已创建，正在后台执行",
            pcap_file_path=pcap_file_path,
            protocol_name=protocol_name
        )
        
        # 异步执行测试
        asyncio.create_task(self._execute_pcap_test(task.task_id))
        
        return TaskResponse(
            task_id=task.task_id,
            status=task.status,
            message=task.message,
            created_at=task.created_at
        )
    
    async def start_pcap_upload_test(
        self,
        file_content: bytes,
        filename: str,
        protocol_name: str,
        remote_ip: Optional[str] = None,
        ssh_user: Optional[str] = None,
        wait_seconds: Optional[int] = None,
        log_lines: Optional[int] = None
    ) -> TaskResponse:
        """启动pcap文件上传测试任务

        Args:
            file_content: pcap文件内容（字节数据）
            filename: 文件名
            protocol_name: 测试协议名称
            remote_ip: 远程服务器IP
            ssh_user: SSH用户名
            wait_seconds: 等待处理时间
            log_lines: 日志输出行数

        Returns:
            任务响应
        """
        # 使用默认值，处理空字符串
        remote_ip = remote_ip if remote_ip and remote_ip.strip() else settings.default_remote_ip
        ssh_user = ssh_user if ssh_user and ssh_user.strip() else settings.default_ssh_user
        wait_seconds = wait_seconds or settings.default_wait_seconds
        log_lines = log_lines or settings.default_log_lines
        
        # 创建任务
        task = await self.task_manager.create_test_task(
            remote_ip=remote_ip,
            ssh_user=ssh_user,
            wait_seconds=wait_seconds,
            log_lines=log_lines,
            message=f"pcap上传测试任务已创建，正在后台执行 ({filename})",
            filename=filename,
            protocol_name=protocol_name
        )
        
        # 异步执行上传测试
        asyncio.create_task(self._execute_pcap_upload_test(task.task_id, file_content, filename))
        
        return TaskResponse(
            task_id=task.task_id,
            status=task.status,
            message=task.message,
            created_at=task.created_at
        )
    
    async def start_pcap_upload_replay_test(
        self,
        file_content: bytes,
        filename: str,
        protocol_name: str,
        remote_ip: Optional[str] = None,
        ssh_user: Optional[str] = None,
        eth_name: Optional[str] = None,
        wait_seconds: Optional[int] = None,
        log_lines: Optional[int] = None
    ) -> TaskResponse:
        """启动pcap文件回放测试任务

        Args:
            file_content: pcap文件内容（字节数据）
            filename: 文件名
            protocol_name: 测试协议名称
            remote_ip: 远程服务器IP
            ssh_user: SSH用户名
            eth_name: 网口名称
            wait_seconds: 等待处理时间
            log_lines: 日志输出行数

        Returns:
            任务响应
        """
        # 使用默认值，处理空字符串
        remote_ip = remote_ip if remote_ip and remote_ip.strip() else settings.default_remote_ip
        ssh_user = ssh_user if ssh_user and ssh_user.strip() else settings.default_ssh_user
        eth_name = eth_name if eth_name and eth_name.strip() else settings.default_eth_name
        wait_seconds = wait_seconds or settings.default_wait_seconds
        log_lines = log_lines or settings.default_log_lines
        
        # 创建任务
        task = await self.task_manager.create_test_task(
            remote_ip=remote_ip,
            ssh_user=ssh_user,
            wait_seconds=wait_seconds,
            log_lines=log_lines,
            message=f"pcap回放测试任务已创建，正在后台执行 ({filename})",
            filename=filename,
            eth_name=eth_name,
            protocol_name=protocol_name
        )
        
        # 异步执行回放测试
        asyncio.create_task(self._execute_pcap_replay_test(task.task_id, file_content, filename))
        
        return TaskResponse(
            task_id=task.task_id,
            status=task.status,
            message=task.message,
            created_at=task.created_at
        )
    
    async def _execute_pcap_test(self, task_id: str) -> None:
        """执行pcap测试任务
        
        Args:
            task_id: 任务ID
        """
        try:
            task = await self.task_manager.get_task(task_id)
            if not task:
                logger.error(f"任务不存在: {task_id}")
                return
            
            logger.info(f"开始执行pcap测试任务: {task_id}")
            
            # 更新任务状态为运行中
            await self.task_manager.update_task_status(
                task_id, TaskStatus.RUNNING, "正在执行pcap测试"
            )
            
            # 第一步：检查远程文件是否存在
            logger.info(f"任务 {task_id} - 步骤1: 检查远程pcap文件是否存在")
            if not await self.ssh_service.file_exists(task.remote_ip, task.ssh_user, task.pcap_file_path):
                await self.task_manager.update_task_status(
                    task_id, TaskStatus.ERROR, f"远程pcap文件不存在: {task.pcap_file_path}"
                )
                return
            
            # 第二步：复制pcap文件到测试目录
            logger.info(f"任务 {task_id} - 步骤2: 复制pcap文件到测试目录")
            copy_command = f'cp "{task.pcap_file_path}" {settings.pcap_task_dir}/'

            _, stderr, return_code = await self.ssh_service.execute_command(
                task.remote_ip, task.ssh_user, copy_command, timeout=60
            )
            
            if return_code != 0:
                await self.task_manager.update_task_status(
                    task_id, TaskStatus.FAILED, f"pcap文件复制失败: {stderr}"
                )
                return
            
            logger.info(f"任务 {task_id} - 步骤2: pcap文件复制成功")
            
            # 第三步：等待处理
            logger.info(f"任务 {task_id} - 步骤3: 等待 {task.wait_seconds} 秒进行处理")
            await asyncio.sleep(task.wait_seconds)
            
            # 第四步：获取日志内容
            logger.info(f"任务 {task_id} - 步骤4: 获取日志内容")
            log_output = await self.ssh_service.get_test_logs(task.remote_ip, task.ssh_user, task.log_lines, task.protocol_name)
            
            # 更新任务状态为完成
            await self.task_manager.update_task_status(
                task_id, TaskStatus.SUCCESS, "pcap测试完成", {"logs": log_output}
            )
            
            logger.info(f"pcap测试任务完成: {task_id}")
            
        except Exception as e:
            logger.error(f"执行pcap测试任务失败: {e}")
            await self.task_manager.update_task_status(
                task_id, TaskStatus.ERROR, f"执行失败: {str(e)}"
            )
    
    async def _execute_pcap_upload_test(self, task_id: str, file_content: bytes, filename: str) -> None:
        """执行pcap上传测试任务
        
        Args:
            task_id: 任务ID
            file_content: 文件内容（字节数据）
            filename: 文件名
        """
        try:
            task = await self.task_manager.get_task(task_id)
            if not task:
                logger.error(f"任务不存在: {task_id}")
                return
            
            logger.info(f"开始执行pcap上传测试任务: {task_id}")
            
            # 更新任务状态为运行中
            await self.task_manager.update_task_status(
                task_id, TaskStatus.RUNNING, "正在执行pcap上传测试"
            )
            
            # 第一步：保存上传的文件到本地临时目录
            logger.info(f"任务 {task_id} - 步骤1: 保存上传文件到本地临时目录")
            with tempfile.NamedTemporaryFile(delete=False, suffix='.pcap') as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name
            
            try:
                # 第二步：使用SSH服务上传文件到远程服务器
                logger.info(f"任务 {task_id} - 步骤2: 上传pcap文件到远程服务器")
                remote_pcap_path = f"{settings.pcap_task_dir}/{filename}"
                
                success = await self.ssh_service.copy_file_to_remote(
                    task.remote_ip, task.ssh_user, temp_file_path, remote_pcap_path
                )
                
                if not success:
                    await self.task_manager.update_task_status(
                        task_id, TaskStatus.FAILED, "pcap文件上传失败"
                    )
                    return
                
                logger.info(f"任务 {task_id} - 步骤2: pcap文件上传成功")
                
                # 第三步：等待处理
                logger.info(f"任务 {task_id} - 步骤3: 等待 {task.wait_seconds} 秒进行处理")
                await asyncio.sleep(task.wait_seconds)
                
                # 第四步：获取日志内容
                logger.info(f"任务 {task_id} - 步骤4: 获取日志内容")
                log_output = await self.ssh_service.get_test_logs(task.remote_ip, task.ssh_user, task.log_lines, task.protocol_name)
                
                # 更新任务状态为完成
                await self.task_manager.update_task_status(
                    task_id, TaskStatus.SUCCESS, "pcap上传测试完成", {"logs": log_output}
                )
                
                logger.info(f"pcap上传测试任务完成: {task_id}")
                
            finally:
                # 清理本地临时文件
                try:
                    os.unlink(temp_file_path)
                except Exception as e:
                    logger.warning(f"清理临时文件失败: {e}")
                
        except Exception as e:
            logger.error(f"执行pcap上传测试任务失败: {e}")
            await self.task_manager.update_task_status(
                task_id, TaskStatus.ERROR, f"执行失败: {str(e)}"
            )
    
    async def _execute_pcap_replay_test(self, task_id: str, file_content: bytes, filename: str) -> None:
        """执行pcap回放测试任务
        
        Args:
            task_id: 任务ID
            file_content: 文件内容（字节数据）
            filename: 文件名
        """
        try:
            task = await self.task_manager.get_task(task_id)
            if not task:
                logger.error(f"任务不存在: {task_id}")
                return
            
            logger.info(f"开始执行pcap回放测试任务: {task_id}")
            
            # 更新任务状态为运行中
            await self.task_manager.update_task_status(
                task_id, TaskStatus.RUNNING, "正在执行pcap回放测试"
            )
            
            # 第一步：保存上传的文件到本地临时目录
            logger.info(f"任务 {task_id} - 步骤1: 保存上传文件到本地临时目录")
            with tempfile.NamedTemporaryFile(delete=False, suffix='.pcap') as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name
            
            try:
                # 第二步：使用SSH服务上传文件到远程服务器
                logger.info(f"任务 {task_id} - 步骤2: 上传pcap文件到远程服务器")
                remote_pcap_path = f"{settings.pcap_replay_dir}/{filename}"
                
                success = await self.ssh_service.copy_file_to_remote(
                    task.remote_ip, task.ssh_user, temp_file_path, remote_pcap_path
                )
                
                if not success:
                    await self.task_manager.update_task_status(
                        task_id, TaskStatus.FAILED, "pcap文件上传失败"
                    )
                    return
                
                logger.info(f"任务 {task_id} - 步骤2: pcap文件上传成功")
                
                # 第三步：执行tcpreplay命令进行pcap回放
                logger.info(f"任务 {task_id} - 步骤3: 执行tcpreplay回放pcap文件")
                replay_command = f"tcpreplay -i {task.eth_name} -M 100 {remote_pcap_path}"
                
                stdout, stderr, return_code = await self.ssh_service.execute_command(
                    task.remote_ip, task.ssh_user, replay_command, timeout=300  # 5分钟超时
                )
                
                if return_code != 0:
                    await self.task_manager.update_task_status(
                        task_id, TaskStatus.FAILED, f"tcpreplay执行失败: {stderr}"
                    )
                    return
                
                logger.info(f"任务 {task_id} - 步骤3: tcpreplay执行成功")
                
                # 第四步：等待处理
                logger.info(f"任务 {task_id} - 步骤4: 等待 {task.wait_seconds} 秒进行处理")
                await asyncio.sleep(task.wait_seconds)
                
                # 第五步：获取日志内容
                logger.info(f"任务 {task_id} - 步骤5: 获取日志内容")
                log_output = await self.ssh_service.get_test_logs(task.remote_ip, task.ssh_user, task.log_lines, task.protocol_name)
                
                # 更新任务状态为完成，包含tcpreplay的输出
                result = {
                    "logs": log_output,
                    "tcpreplay_output": {
                        "stdout": stdout,
                        "stderr": stderr,
                        "return_code": return_code
                    }
                }
                
                await self.task_manager.update_task_status(
                    task_id, TaskStatus.SUCCESS, "pcap回放测试完成", result
                )
                
                logger.info(f"pcap回放测试任务完成: {task_id}")
                
            finally:
                # 清理本地临时文件
                try:
                    os.unlink(temp_file_path)
                except Exception as e:
                    logger.warning(f"清理临时文件失败: {e}")
                
                # 清理远程pcap文件
                try:
                    cleanup_command = f"rm -f {remote_pcap_path}"
                    await self.ssh_service.execute_command(
                        task.remote_ip, task.ssh_user, cleanup_command, timeout=30
                    )
                    logger.info(f"任务 {task_id} - 清理远程pcap文件: {remote_pcap_path}")
                except Exception as e:
                    logger.warning(f"清理远程pcap文件失败: {e}")
                
        except Exception as e:
            logger.error(f"执行pcap回放测试任务失败: {e}")
            await self.task_manager.update_task_status(
                task_id, TaskStatus.ERROR, f"执行失败: {str(e)}"
            ) 
"""测试服务

提供GWHW网关的pcap文件测试功能。
"""

import asyncio
import os
import tempfile
from typing import Optional, TYPE_CHECKING

from models.task_models import TaskStatus
from models.response_models import TaskResponse
from core.logging import get_logger
from config.settings import settings
from utils.task_manager import TaskManager
from utils.kafka_client import KafkaClient
from .ssh_service import SSHService

if TYPE_CHECKING:
    from .credential_service import CredentialService

logger = get_logger(__name__)


class TestService:
    """测试服务"""

    def __init__(self, task_manager: TaskManager, credential_service: Optional["CredentialService"] = None):
        self.task_manager = task_manager
        self.ssh_service = SSHService(credential_service)
    
    async def start_pcap_test(
        self,
        pcap_file_path: str,
        protocol_name: str,
        remote_ip: Optional[str] = None,
        ssh_user: Optional[str] = None,
        wait_seconds: Optional[int] = None,
        log_lines: Optional[int] = None
    ) -> TaskResponse:
        """启动pcap文件测试任务

        Args:
            pcap_file_path: pcap文件路径
            protocol_name: 测试协议名称
            remote_ip: 远程服务器IP
            ssh_user: SSH用户名
            wait_seconds: 等待处理时间
            log_lines: 日志输出行数

        Returns:
            任务响应
        """
        # 使用默认值，处理空字符串
        remote_ip = remote_ip if remote_ip and remote_ip.strip() else settings.default_remote_ip
        ssh_user = ssh_user if ssh_user and ssh_user.strip() else settings.default_ssh_user
        wait_seconds = wait_seconds or settings.default_wait_seconds
        log_lines = log_lines or settings.default_log_lines
        
        # 创建任务
        task = await self.task_manager.create_test_task(
            remote_ip=remote_ip,
            ssh_user=ssh_user,
            wait_seconds=wait_seconds,
            log_lines=log_lines,
            message="pcap测试任务已创建，正在后台执行",
            pcap_file_path=pcap_file_path,
            protocol_name=protocol_name
        )
        
        # 异步执行测试
        asyncio.create_task(self._execute_pcap_test(task.task_id))
        
        return TaskResponse(
            task_id=task.task_id,
            status=task.status,
            message=task.message,
            created_at=task.created_at
        )
    
    async def start_pcap_upload_test(
        self,
        file_content: bytes,
        filename: str,
        protocol_name: str,
        remote_ip: Optional[str] = None,
        ssh_user: Optional[str] = None,
        wait_seconds: Optional[int] = None,
        log_lines: Optional[int] = None
    ) -> TaskResponse:
        """启动pcap文件上传测试任务

        Args:
            file_content: pcap文件内容（字节数据）
            filename: 文件名
            protocol_name: 测试协议名称
            remote_ip: 远程服务器IP
            ssh_user: SSH用户名
            wait_seconds: 等待处理时间
            log_lines: 日志输出行数

        Returns:
            任务响应
        """
        # 使用默认值，处理空字符串
        remote_ip = remote_ip if remote_ip and remote_ip.strip() else settings.default_remote_ip
        ssh_user = ssh_user if ssh_user and ssh_user.strip() else settings.default_ssh_user
        wait_seconds = wait_seconds or settings.default_wait_seconds
        log_lines = log_lines or settings.default_log_lines
        
        # 创建任务
        task = await self.task_manager.create_test_task(
            remote_ip=remote_ip,
            ssh_user=ssh_user,
            wait_seconds=wait_seconds,
            log_lines=log_lines,
            message=f"pcap上传测试任务已创建，正在后台执行 ({filename})",
            filename=filename,
            protocol_name=protocol_name
        )
        
        # 异步执行上传测试
        asyncio.create_task(self._execute_pcap_upload_test(task.task_id, file_content, filename))
        
        return TaskResponse(
            task_id=task.task_id,
            status=task.status,
            message=task.message,
            created_at=task.created_at
        )
    
    async def start_pcap_upload_replay_test(
        self,
        file_content: bytes,
        filename: str,
        protocol_name: str,
        remote_ip: Optional[str] = None,
        ssh_user: Optional[str] = None,
        eth_name: Optional[str] = None,
        wait_seconds: Optional[int] = None,
        log_lines: Optional[int] = None
    ) -> TaskResponse:
        """启动pcap文件回放测试任务

        Args:
            file_content: pcap文件内容（字节数据）
            filename: 文件名
            protocol_name: 测试协议名称
            remote_ip: 远程服务器IP
            ssh_user: SSH用户名
            eth_name: 网口名称
            wait_seconds: 等待处理时间
            log_lines: 日志输出行数

        Returns:
            任务响应
        """
        # 使用默认值，处理空字符串
        remote_ip = remote_ip if remote_ip and remote_ip.strip() else settings.default_remote_ip
        ssh_user = ssh_user if ssh_user and ssh_user.strip() else settings.default_ssh_user
        eth_name = eth_name if eth_name and eth_name.strip() else settings.default_eth_name
        wait_seconds = wait_seconds or settings.default_wait_seconds
        log_lines = log_lines or settings.default_log_lines
        
        # 创建任务
        task = await self.task_manager.create_test_task(
            remote_ip=remote_ip,
            ssh_user=ssh_user,
            wait_seconds=wait_seconds,
            log_lines=log_lines,
            message=f"pcap回放测试任务已创建，正在后台执行 ({filename})",
            filename=filename,
            eth_name=eth_name,
            protocol_name=protocol_name
        )
        
        # 异步执行回放测试
        asyncio.create_task(self._execute_pcap_replay_test(task.task_id, file_content, filename))
        
        return TaskResponse(
            task_id=task.task_id,
            status=task.status,
            message=task.message,
            created_at=task.created_at
        )

    async def start_pcap_replay_test(
        self,
        pcap_file_name: str,
        protocol_name: str,
        remote_ip: Optional[str] = None,
        ssh_user: Optional[str] = None,
        eth_name: Optional[str] = None,
        wait_seconds: Optional[int] = None,
        log_lines: Optional[int] = None
    ) -> TaskResponse:
        """启动pcap文件回放测试任务（使用服务器上已存在的文件）

        Args:
            pcap_file_name: pcap文件名（不包含路径）
            protocol_name: 测试协议名称
            remote_ip: 远程服务器IP
            ssh_user: SSH用户名
            eth_name: 网络接口名称
            wait_seconds: 等待处理时间
            log_lines: 日志输出行数

        Returns:
            任务响应
        """
        # 使用默认值，处理空字符串
        remote_ip = remote_ip if remote_ip and remote_ip.strip() else settings.default_remote_ip
        ssh_user = ssh_user if ssh_user and ssh_user.strip() else settings.default_ssh_user
        eth_name = eth_name if eth_name and eth_name.strip() else settings.default_eth_name
        wait_seconds = wait_seconds or settings.default_wait_seconds
        log_lines = log_lines or settings.default_log_lines

        # 创建任务
        task = await self.task_manager.create_test_task(
            remote_ip=remote_ip,
            ssh_user=ssh_user,
            wait_seconds=wait_seconds,
            log_lines=log_lines,
            message=f"pcap回放测试任务已创建，正在后台执行 ({pcap_file_name})",
            pcap_file_name=pcap_file_name,
            eth_name=eth_name,
            protocol_name=protocol_name
        )

        # 异步执行回放测试
        asyncio.create_task(self._execute_pcap_replay_existing_test(task.task_id))

        return TaskResponse(
            task_id=task.task_id,
            status=task.status,
            message=task.message,
            created_at=task.created_at
        )

    async def _execute_pcap_test(self, task_id: str) -> None:
        """执行pcap测试任务
        
        Args:
            task_id: 任务ID
        """
        try:
            task = await self.task_manager.get_task(task_id)
            if not task:
                logger.error(f"任务不存在: {task_id}")
                return
            
            logger.info(f"开始执行pcap测试任务: {task_id}")
            
            # 更新任务状态为运行中
            await self.task_manager.update_task_status(
                task_id, TaskStatus.RUNNING, "正在执行pcap测试"
            )
            
            # 第一步：检查远程文件是否存在
            logger.info(f"任务 {task_id} - 步骤1: 检查远程pcap文件是否存在")
            if not await self.ssh_service.file_exists(task.remote_ip, task.ssh_user, task.pcap_file_path):
                await self.task_manager.update_task_status(
                    task_id, TaskStatus.ERROR, f"远程pcap文件不存在: {task.pcap_file_path}"
                )
                return
            
            # 第二步：复制pcap文件到测试目录
            logger.info(f"任务 {task_id} - 步骤2: 复制pcap文件到测试目录")
            copy_command = f'cp "{task.pcap_file_path}" {settings.pcap_task_dir}/'

            _, stderr, return_code = await self.ssh_service.execute_command(
                task.remote_ip, task.ssh_user, copy_command, timeout=60
            )
            
            if return_code != 0:
                await self.task_manager.update_task_status(
                    task_id, TaskStatus.FAILED, f"pcap文件复制失败: {stderr}"
                )
                return
            
            logger.info(f"任务 {task_id} - 步骤2: pcap文件复制成功")
            
            # 第三步：等待处理
            logger.info(f"任务 {task_id} - 步骤3: 等待 {task.wait_seconds} 秒进行处理")
            await asyncio.sleep(task.wait_seconds)
            
            # 第四步：获取日志内容
            logger.info(f"任务 {task_id} - 步骤4: 获取日志内容")
            log_output = await self.ssh_service.get_test_logs(task.remote_ip, task.ssh_user, task.log_lines, task.protocol_name)
            
            # 更新任务状态为完成
            await self.task_manager.update_task_status(
                task_id, TaskStatus.SUCCESS, "pcap测试完成", {"logs": log_output}
            )
            
            logger.info(f"pcap测试任务完成: {task_id}")
            
        except Exception as e:
            logger.error(f"执行pcap测试任务失败: {e}")
            await self.task_manager.update_task_status(
                task_id, TaskStatus.ERROR, f"执行失败: {str(e)}"
            )
    
    async def _execute_pcap_upload_test(self, task_id: str, file_content: bytes, filename: str) -> None:
        """执行pcap上传测试任务
        
        Args:
            task_id: 任务ID
            file_content: 文件内容（字节数据）
            filename: 文件名
        """
        try:
            task = await self.task_manager.get_task(task_id)
            if not task:
                logger.error(f"任务不存在: {task_id}")
                return
            
            logger.info(f"开始执行pcap上传测试任务: {task_id}")
            
            # 更新任务状态为运行中
            await self.task_manager.update_task_status(
                task_id, TaskStatus.RUNNING, "正在执行pcap上传测试"
            )
            
            # 第一步：保存上传的文件到本地临时目录
            logger.info(f"任务 {task_id} - 步骤1: 保存上传文件到本地临时目录")
            with tempfile.NamedTemporaryFile(delete=False, suffix='.pcap') as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name
            
            try:
                # 第二步：使用SSH服务上传文件到远程服务器
                logger.info(f"任务 {task_id} - 步骤2: 上传pcap文件到远程服务器")
                remote_pcap_path = f"{settings.pcap_task_dir}/{filename}"
                
                success = await self.ssh_service.copy_file_to_remote(
                    task.remote_ip, task.ssh_user, temp_file_path, remote_pcap_path
                )
                
                if not success:
                    await self.task_manager.update_task_status(
                        task_id, TaskStatus.FAILED, "pcap文件上传失败"
                    )
                    return
                
                logger.info(f"任务 {task_id} - 步骤2: pcap文件上传成功")
                
                # 第三步：等待处理
                logger.info(f"任务 {task_id} - 步骤3: 等待 {task.wait_seconds} 秒进行处理")
                await asyncio.sleep(task.wait_seconds)
                
                # 第四步：获取日志内容
                logger.info(f"任务 {task_id} - 步骤4: 获取日志内容")
                log_output = await self.ssh_service.get_test_logs(task.remote_ip, task.ssh_user, task.log_lines, task.protocol_name)
                
                # 更新任务状态为完成
                await self.task_manager.update_task_status(
                    task_id, TaskStatus.SUCCESS, "pcap上传测试完成", {"logs": log_output}
                )
                
                logger.info(f"pcap上传测试任务完成: {task_id}")
                
            finally:
                # 清理本地临时文件
                try:
                    os.unlink(temp_file_path)
                except Exception as e:
                    logger.warning(f"清理临时文件失败: {e}")
                
        except Exception as e:
            logger.error(f"执行pcap上传测试任务失败: {e}")
            await self.task_manager.update_task_status(
                task_id, TaskStatus.ERROR, f"执行失败: {str(e)}"
            )
    
    async def _execute_pcap_replay_test(self, task_id: str, file_content: bytes, filename: str) -> None:
        """执行pcap回放测试任务
        
        Args:
            task_id: 任务ID
            file_content: 文件内容（字节数据）
            filename: 文件名
        """
        try:
            task = await self.task_manager.get_task(task_id)
            if not task:
                logger.error(f"任务不存在: {task_id}")
                return
            
            logger.info(f"开始执行pcap回放测试任务: {task_id}")
            
            # 更新任务状态为运行中
            await self.task_manager.update_task_status(
                task_id, TaskStatus.RUNNING, "正在执行pcap回放测试"
            )
            
            # 第一步：保存上传的文件到本地临时目录
            logger.info(f"任务 {task_id} - 步骤1: 保存上传文件到本地临时目录")
            with tempfile.NamedTemporaryFile(delete=False, suffix='.pcap') as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name
            
            try:
                # 第二步：使用SSH服务上传文件到远程服务器
                logger.info(f"任务 {task_id} - 步骤2: 上传pcap文件到远程服务器")
                remote_pcap_path = f"{settings.pcap_replay_dir}/{filename}"
                
                success = await self.ssh_service.copy_file_to_remote(
                    task.remote_ip, task.ssh_user, temp_file_path, remote_pcap_path
                )
                
                if not success:
                    await self.task_manager.update_task_status(
                        task_id, TaskStatus.FAILED, "pcap文件上传失败"
                    )
                    return
                
                logger.info(f"任务 {task_id} - 步骤2: pcap文件上传成功")
                
                # 第三步：执行tcpreplay命令进行pcap回放
                logger.info(f"任务 {task_id} - 步骤3: 执行tcpreplay回放pcap文件")
                replay_command = f"tcpreplay -i {task.eth_name} -M 100 {remote_pcap_path}"
                
                stdout, stderr, return_code = await self.ssh_service.execute_command(
                    task.remote_ip, task.ssh_user, replay_command, timeout=300  # 5分钟超时
                )
                
                if return_code != 0:
                    await self.task_manager.update_task_status(
                        task_id, TaskStatus.FAILED, f"tcpreplay执行失败: {stderr}"
                    )
                    return
                
                logger.info(f"任务 {task_id} - 步骤3: tcpreplay执行成功")
                
                # 第四步：等待处理
                logger.info(f"任务 {task_id} - 步骤4: 等待 {task.wait_seconds} 秒进行处理")
                await asyncio.sleep(task.wait_seconds)
                
                # 第五步：获取日志内容
                logger.info(f"任务 {task_id} - 步骤5: 获取日志内容")
                log_output = await self.ssh_service.get_test_logs(task.remote_ip, task.ssh_user, task.log_lines, task.protocol_name)
                
                # 更新任务状态为完成，包含tcpreplay的输出
                result = {
                    "logs": log_output,
                    "tcpreplay_output": {
                        "stdout": stdout,
                        "stderr": stderr,
                        "return_code": return_code
                    }
                }
                
                await self.task_manager.update_task_status(
                    task_id, TaskStatus.SUCCESS, "pcap回放测试完成", result
                )
                
                logger.info(f"pcap回放测试任务完成: {task_id}")
                
            finally:
                # 清理本地临时文件
                try:
                    os.unlink(temp_file_path)
                except Exception as e:
                    logger.warning(f"清理临时文件失败: {e}")
                
                # 清理远程pcap文件
                try:
                    cleanup_command = f"rm -f {remote_pcap_path}"
                    await self.ssh_service.execute_command(
                        task.remote_ip, task.ssh_user, cleanup_command, timeout=30
                    )
                    logger.info(f"任务 {task_id} - 清理远程pcap文件: {remote_pcap_path}")
                except Exception as e:
                    logger.warning(f"清理远程pcap文件失败: {e}")
                
        except Exception as e:
            logger.error(f"执行pcap回放测试任务失败: {e}")
            await self.task_manager.update_task_status(
                task_id, TaskStatus.ERROR, f"执行失败: {str(e)}"
            )

    async def _execute_pcap_replay_existing_test(self, task_id: str) -> None:
        """执行pcap回放测试任务（使用服务器上已存在的文件）

        Args:
            task_id: 任务ID
        """
        try:
            task = await self.task_manager.get_task(task_id)
            if not task:
                logger.error(f"任务不存在: {task_id}")
                return

            logger.info(f"开始执行pcap回放测试任务（已存在文件）: {task_id}")

            # 更新任务状态为运行中
            await self.task_manager.update_task_status(
                task_id, TaskStatus.RUNNING, "正在执行pcap回放测试"
            )

            # 第一步：构建pcap文件完整路径
            logger.info(f"任务 {task_id} - 步骤1: 构建pcap文件路径")
            pcap_file_path = f"{settings.pcap_test_dir}{task.protocol_name}/{task.pcap_file_name}"
            logger.info(f"任务 {task_id} - pcap文件路径: {pcap_file_path}")

            # 第二步：检查文件是否存在
            logger.info(f"任务 {task_id} - 步骤2: 检查pcap文件是否存在")
            check_command = f"test -f {pcap_file_path} && echo 'exists' || echo 'not_found'"

            stdout, stderr, return_code = await self.ssh_service.execute_command(
                task.remote_ip, task.ssh_user, check_command, timeout=30
            )

            if return_code != 0 or stdout.strip() != 'exists':
                await self.task_manager.update_task_status(
                    task_id, TaskStatus.FAILED, f"pcap文件不存在: {pcap_file_path}"
                )
                return

            logger.info(f"任务 {task_id} - 步骤2: pcap文件存在，继续执行")

            # 第三步：执行tcpreplay命令进行pcap回放，并实时收集Kafka数据
            logger.info(f"任务 {task_id} - 步骤3: 执行tcpreplay回放pcap文件并收集实时数据")

            # 使用专门的方法执行回放并收集实时Kafka数据
            replay_result = await self._execute_replay_with_realtime_kafka(
                task_id, task, pcap_file_path
            )

            if not replay_result["success"]:
                await self.task_manager.update_task_status(
                    task_id, TaskStatus.FAILED, replay_result["error_message"]
                )
                return

            logger.info(f"任务 {task_id} - 步骤3: tcpreplay执行成功，收集到 {len(replay_result['kafka_events'])} 个Kafka事件")

            # 第四步：获取其他日志内容（hw.log 和 hw.err）
            logger.info(f"任务 {task_id} - 步骤4: 获取其他日志内容")
            log_output = await self.ssh_service.get_test_logs_without_kafka(
                task.remote_ip, task.ssh_user, task.log_lines, task.protocol_name
            )

            # 将实时收集的Kafka事件添加到日志输出中
            log_output["json_events"] = replay_result["kafka_events"]

            # 更新任务状态为完成，包含tcpreplay的输出和实时Kafka数据
            result = {
                "logs": log_output,
                "tcpreplay_output": replay_result["tcpreplay_output"],
                "pcap_file_path": pcap_file_path,
                "kafka_events_count": len(replay_result["kafka_events"]),
                "realtime_data_collection": True
            }

            await self.task_manager.update_task_status(
                task_id, TaskStatus.SUCCESS, "pcap回放测试完成", result
            )

            logger.info(f"pcap回放测试任务完成: {task_id}")

        except Exception as e:
            logger.error(f"执行pcap回放测试任务失败: {e}")
            await self.task_manager.update_task_status(
                task_id, TaskStatus.ERROR, f"执行失败: {str(e)}"
            )

    async def _execute_replay_with_realtime_kafka(self, task_id: str, task, pcap_file_path: str) -> dict:
        """执行pcap回放并实时收集Kafka数据

        Args:
            task_id: 任务ID
            task: 任务对象
            pcap_file_path: pcap文件路径

        Returns:
            包含执行结果和Kafka事件的字典
        """

        kafka_events = []
        tcpreplay_output = {"stdout": "", "stderr": "", "return_code": -1}

        try:
            # 创建专用的Kafka客户端用于实时数据收集
            async with KafkaClient() as kafka_client:
                logger.info(f"任务 {task_id} - 已建立实时Kafka连接")

                # 启动Kafka数据收集任务（在后台运行）
                kafka_collection_task = asyncio.create_task(
                    self._collect_kafka_events_during_replay(
                        kafka_client, task_id, task.wait_seconds + 10  # 额外10秒缓冲时间
                    )
                )

                # 稍等一下确保Kafka消费者准备就绪
                await asyncio.sleep(1)

                # 执行tcpreplay命令
                logger.info(f"任务 {task_id} - 开始执行tcpreplay命令")
                replay_command = f"tcpreplay -i {task.eth_name} -M 100 {pcap_file_path}"

                stdout, stderr, return_code = await self.ssh_service.execute_command(
                    task.remote_ip, task.ssh_user, replay_command, timeout=300
                )

                tcpreplay_output = {
                    "stdout": stdout,
                    "stderr": stderr,
                    "return_code": return_code
                }

                if return_code != 0:
                    # 取消Kafka收集任务
                    kafka_collection_task.cancel()
                    return {
                        "success": False,
                        "error_message": f"tcpreplay执行失败: {stderr}",
                        "tcpreplay_output": tcpreplay_output,
                        "kafka_events": []
                    }

                logger.info(f"任务 {task_id} - tcpreplay执行成功，等待数据处理")

                # 等待指定时间让系统处理数据包
                await asyncio.sleep(task.wait_seconds)

                # 停止Kafka数据收集并获取结果
                kafka_collection_task.cancel()
                try:
                    kafka_events = await kafka_collection_task
                except asyncio.CancelledError:
                    # 任务被取消，获取已收集的数据
                    kafka_events = getattr(kafka_collection_task, '_collected_events', [])

                logger.info(f"任务 {task_id} - 实时数据收集完成，共收集 {len(kafka_events)} 个事件")

                return {
                    "success": True,
                    "tcpreplay_output": tcpreplay_output,
                    "kafka_events": kafka_events
                }

        except Exception as e:
            logger.error(f"执行回放和Kafka数据收集失败: {e}")
            return {
                "success": False,
                "error_message": f"执行回放和数据收集失败: {str(e)}",
                "tcpreplay_output": tcpreplay_output,
                "kafka_events": kafka_events
            }

    async def _collect_kafka_events_during_replay(self, kafka_client: KafkaClient, task_id: str, total_timeout: int) -> list:
        """在回放期间持续收集Kafka事件

        Args:
            kafka_client: Kafka客户端实例
            task_id: 任务ID
            total_timeout: 总超时时间

        Returns:
            收集到的Kafka事件列表
        """
        collected_events = []
        start_time = asyncio.get_event_loop().time()

        try:
            while asyncio.get_event_loop().time() - start_time < total_timeout:
                try:
                    # 每次收集2秒的数据
                    events = await kafka_client.consume_events(timeout_seconds=2, max_records=50)
                    if events:
                        collected_events.extend(events)
                        logger.debug(f"任务 {task_id} - 收集到 {len(events)} 个新的Kafka事件")

                    # 短暂休息避免过度消费资源
                    await asyncio.sleep(0.5)

                except Exception as e:
                    logger.warning(f"任务 {task_id} - Kafka事件收集出现异常: {e}")
                    await asyncio.sleep(1)

        except asyncio.CancelledError:
            logger.info(f"任务 {task_id} - Kafka事件收集被取消，已收集 {len(collected_events)} 个事件")
            raise

        return collected_events
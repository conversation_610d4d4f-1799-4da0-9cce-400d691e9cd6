"""SSH连接服务

提供SSH连接和远程命令执行功能。
"""

import asyncio
import asyncssh
import json
from typing import Optional, Any, TYPE_CHECKING
from core.logging import get_logger
from core.exceptions import SSHConnectionException
from config.settings import settings
from utils.kafka_client import get_kafka_events

if TYPE_CHECKING:
    from services.credential_service import CredentialService

logger = get_logger(__name__)


class SSHService:
    """SSH连接服务"""

    def __init__(self, credential_service: Optional["CredentialService"] = None):
        self.credential_service = credential_service
        self.connection_options = {
            'connect_timeout': settings.ssh_timeout,
            'keepalive_interval': settings.ssh_keepalive_interval,
            'keepalive_count_max': settings.ssh_keepalive_count_max,
            'known_hosts': None,  # 跳过主机密钥检查
        }

    async def _get_credentials_for_host(self, host: str, user: Optional[str] = None, password: Optional[str] = None) -> tuple[str, Optional[str]]:
        """获取主机的SSH凭据

        Args:
            host: 主机IP
            user: 指定的用户名（可选）
            password: 指定的密码（可选）

        Returns:
            (用户名, 密码) 元组
        """
        # 如果已经提供了用户名和密码，直接使用
        if user and password:
            return user, password

        # 如果有凭据服务，尝试从中获取
        if self.credential_service:
            try:
                credential_info = await self.credential_service.get_credential_for_ssh(host)
                if credential_info:
                    final_user = user or credential_info.get("ssh_user")
                    final_password = password or credential_info.get("ssh_password")
                    if final_user:
                        logger.info(f"从凭据存储获取到 {host} 的SSH凭据")
                        return final_user, final_password
            except Exception as e:
                logger.warning(f"从凭据存储获取 {host} 的SSH凭据失败: {e}")

        # 回退到配置文件中的默认值
        final_user = user or settings.default_ssh_user
        final_password = password or settings.default_ssh_password

        logger.debug(f"使用默认SSH凭据连接 {host}")
        return final_user, final_password
    
    async def execute_command(
        self,
        host: str,
        user: Optional[str] = None,
        command: str = "",
        timeout: Optional[int] = None,
        password: Optional[str] = None
    ) -> tuple[str, str, int]:
        """执行远程命令

        Args:
            host: 远程主机IP
            user: SSH用户名（可选，会自动从凭据存储获取）
            command: 要执行的命令
            timeout: 超时时间（秒）
            password: SSH密码（可选，会自动从凭据存储获取）

        Returns:
            (stdout, stderr, return_code)

        Raises:
            SSHConnectionException: SSH连接失败
        """
        try:
            # 自动获取凭据
            final_user, final_password = await self._get_credentials_for_host(host, user, password)

            logger.info(f"连接SSH: {final_user}@{host}")

            # 准备连接选项
            connect_options = self.connection_options.copy()
            if final_password:
                connect_options['password'] = final_password
                logger.debug("使用密码认证")
            else:
                logger.debug("使用密钥认证")

            async with asyncssh.connect(
                host,
                username=final_user,
                **connect_options
            ) as conn:
                logger.debug(f"执行命令: {command}")

                result = await conn.run(
                    command,
                    timeout=timeout,
                    check=False  # 不检查返回码，允许非零返回
                )

                stdout = result.stdout or ""
                stderr = result.stderr or ""
                return_code = result.exit_status or 0

                logger.info(f"命令执行完成，返回码: {return_code}")
                return stdout, stderr, return_code

        except asyncssh.Error as e:
            error_msg = f"SSH连接错误: {e}"
            logger.error(error_msg)
            raise SSHConnectionException(host, str(e))
        except asyncio.TimeoutError:
            error_msg = "SSH命令执行超时"
            logger.error(error_msg)
            raise SSHConnectionException(host, error_msg)
        except Exception as e:
            error_msg = f"SSH执行异常: {e}"
            logger.error(error_msg)
            raise SSHConnectionException(host, str(e))
    
    async def check_container_running(
        self,
        host: str,
        user: Optional[str] = None,
        container_name: str = ""
    ) -> bool:
        """检查容器是否运行
        
        Args:
            host: 远程主机IP
            user: SSH用户名
            container_name: 容器名称
            
        Returns:
            容器是否运行
        """
        try:
            command = f"docker ps --filter name={container_name} --filter status=running --quiet"
            stdout, stderr, return_code = await self.execute_command(
                host, user, command, timeout=30
            )
            
            if return_code == 0 and stdout.strip():
                logger.info(f"容器 {container_name} 正在运行")
                return True
            else:
                logger.warning(f"容器 {container_name} 未运行")
                return False
                
        except Exception as e:
            logger.error(f"检查容器状态失败: {e}")
            return False
    
    async def copy_file_to_remote(
        self,
        host: str,
        user: Optional[str],
        local_path: str,
        remote_path: str,
        password: Optional[str] = None
    ) -> bool:
        """复制文件到远程服务器

        Args:
            host: 远程主机IP
            user: SSH用户名
            local_path: 本地文件路径
            remote_path: 远程文件路径
            password: SSH密码（可选）

        Returns:
            是否成功
        """
        try:
            # 自动获取凭据
            final_user, final_password = await self._get_credentials_for_host(host, user, password)

            logger.info(f"复制文件: {local_path} -> {final_user}@{host}:{remote_path}")

            # 准备连接选项
            connect_options = self.connection_options.copy()
            if final_password:
                connect_options['password'] = final_password

            async with asyncssh.connect(
                host,
                username=final_user,
                **connect_options
            ) as conn:
                await asyncssh.scp(local_path, (conn, remote_path))
                logger.info("文件复制成功")
                return True

        except Exception as e:
            logger.error(f"文件复制失败: {e}")
            return False
    
    async def copy_file_from_remote(
        self,
        host: str,
        user: Optional[str],
        remote_path: str,
        local_path: str,
        password: Optional[str] = None
    ) -> bool:
        """从远程服务器复制文件

        Args:
            host: 远程主机IP
            user: SSH用户名
            remote_path: 远程文件路径
            local_path: 本地文件路径
            password: SSH密码（可选）

        Returns:
            是否成功
        """
        try:
            # 自动获取凭据
            final_user, final_password = await self._get_credentials_for_host(host, user, password)

            logger.info(f"复制文件: {final_user}@{host}:{remote_path} -> {local_path}")

            # 准备连接选项
            connect_options = self.connection_options.copy()
            if final_password:
                connect_options['password'] = final_password

            async with asyncssh.connect(
                host,
                username=final_user,
                **connect_options
            ) as conn:
                await asyncssh.scp((conn, remote_path), local_path)
                logger.info("文件复制成功")
                return True

        except Exception as e:
            logger.error(f"文件复制失败: {e}")
            return False

    async def file_exists(self, host: str, user: Optional[str], file_path: str) -> bool:
        """检查远程文件或目录是否存在

        Args:
            host: 远程主机IP
            user: SSH用户名
            file_path: 文件或目录路径

        Returns:
            bool: 文件或目录是否存在
        """
        try:
            # 使用 -e 检查文件或目录是否存在（而不是只检查文件）
            check_command = f'test -e "{file_path}" && echo "exists" || echo "not_exists"'
            stdout, stderr, return_code = await self.execute_command(
                host, user, check_command, timeout=30
            )

            result = stdout.strip() == "exists"
            logger.debug(f"路径存在性检查 {file_path}: {result} (输出: {stdout.strip()})")
            return result

        except Exception as e:
            logger.error(f"检查文件是否存在失败: {e}")
            return False

    async def get_path_type(self, host: str, user: Optional[str], path: str) -> str:
        """获取远程路径的类型

        Args:
            host: 远程主机IP
            user: SSH用户名
            path: 路径

        Returns:
            str: 路径类型 ('file', 'directory', 'not_exists', 'unknown')
        """
        try:
            # 检查路径类型的命令
            check_command = f'''
            if [ -f "{path}" ]; then
                echo "file"
            elif [ -d "{path}" ]; then
                echo "directory"
            elif [ -e "{path}" ]; then
                echo "unknown"
            else
                echo "not_exists"
            fi
            '''

            stdout, _, _ = await self.execute_command(
                host, user, check_command, timeout=30
            )

            path_type = stdout.strip()
            logger.debug(f"路径类型检查 {path}: {path_type}")
            return path_type

        except Exception as e:
            logger.error(f"检查路径类型失败: {e}")
            return "unknown"

    async def get_test_logs(self, host: str, user: Optional[str], log_lines: int, protocol_name: Optional[str] = None) -> dict[str, Any]:
        """获取测试日志

        Args:
            host: 远程主机IP
            user: SSH用户名
            log_lines: 日志行数
            protocol_name: 测试协议名称

        Returns:
            Dict[str, Any]: 日志内容
        """
        try:
            # 获取hw.log内容，根据协议名称过滤相关统计信息
            if protocol_name:
                # 获取协议相关的完整统计信息，包括stats表头、协议统计和qps统计
                # 使用更宽松的过滤条件，确保获取完整的统计信息块
                hw_log_command = f'''tail -{log_lines * 2} {settings.hw_log_path} 2>/dev/null | grep -A 20 -B 5 -E "(stats {protocol_name}|stats qps|{protocol_name} session|{protocol_name} parser|{protocol_name} match|{protocol_name} request|{protocol_name} response)" | grep -E "(stats|{protocol_name}|total|success|failure|all|2s|1m|1h|qps)" || echo "日志文件不存在或为空"'''
            else:
                # 如果没有指定协议，获取全部日志
                hw_log_command = f'tail -{log_lines} {settings.hw_log_path} 2>/dev/null || echo "日志文件不存在或为空"'

            hw_log_stdout, _, _ = await self.execute_command(
                host, user, hw_log_command, timeout=60
            )

            # 处理hw.log重复数据过滤和格式化
            hw_log_processed = self._process_hw_log_output(hw_log_stdout, protocol_name)

            # 获取hw.err内容，只获取包含 [ERROR] 或 [WARN] 等级的日志行
            hw_err_command = f'tail -{log_lines} {settings.hw_err_path} 2>/dev/null | grep -E "\\[(ERROR|WARN)\\]" || echo "错误日志文件不存在或为空"'
            hw_err_stdout, _, _ = await self.execute_command(
                host, user, hw_err_command, timeout=60
            )

            # 处理hw.err格式化
            hw_err_processed = self._format_log_output(hw_err_stdout)
            
            # 获取JSON事件内容，从Kafka消费者获取
            logger.info("从Kafka获取JSON事件数据")
            try:
                json_events = await get_kafka_events(timeout_seconds=settings.kafka_consumer_timeout)
                if not json_events:
                    logger.warning("未从Kafka获取到事件数据，可能是超时或无新消息")
            except Exception as e:
                logger.error(f"从Kafka获取事件数据失败: {e}")
                # 降级处理：如果Kafka不可用，尝试从文件获取（保持向后兼容）
                logger.info("尝试从文件获取JSON事件数据作为降级方案")
                json_command = f'cat {settings.upload_log_file} 2>/dev/null || echo "上传日志文件不存在或为空"'
                json_stdout, _, _ = await self.execute_command(
                    host, user, json_command, timeout=60
                )

                # 解析JSON事件
                json_events = []
                if json_stdout and json_stdout.strip() != "上传日志文件不存在或为空":
                    try:
                        for line in json_stdout.strip().split('\n'):
                            if line.strip():
                                json_events.append(json.loads(line))
                    except json.JSONDecodeError as e:
                        logger.warning(f"JSON解析失败: {e}")
                        json_events = [{"error": f"JSON解析失败: {str(e)}", "raw_content": json_stdout}]
                else:
                    json_events = []
            
            return {
                "hw_log": hw_log_processed,
                "hw_err": hw_err_processed,
                "json_events": json_events
            }
            
        except Exception as e:
            logger.error(f"获取测试日志失败: {e}")
            return {
                "hw_log": f"获取日志失败: {str(e)}",
                "hw_err": "",
                "json_events": []
            }

    def _format_log_output(self, log_content: str) -> str:
        """格式化日志输出，将\\n转换为实际换行符

        Args:
            log_content: 原始日志内容

        Returns:
            格式化后的日志内容
        """
        if not log_content:
            return ""

        # 将字符串中的\\n转换为实际换行符
        formatted_content = log_content.replace('\\n', '\n')

        # 移除多余的空行，但保留必要的换行结构
        lines = formatted_content.split('\n')
        cleaned_lines = []

        for line in lines:
            # 保留非空行和有意义的空行
            if line.strip() or (cleaned_lines and cleaned_lines[-1].strip()):
                cleaned_lines.append(line)

        return '\n'.join(cleaned_lines)

    def _process_hw_log_output(self, log_content: str, protocol_name: Optional[str]) -> str:
        """处理hw.log输出，去除重复数据并格式化

        Args:
            log_content: 原始hw.log内容
            protocol_name: 协议名称

        Returns:
            处理后的日志内容，只包含最新的统计数据块
        """
        if not log_content or not protocol_name:
            return self._format_log_output(log_content)

        # 首先格式化输出
        formatted_content = self._format_log_output(log_content)
        lines = formatted_content.split('\n')

        # 查找最新的stats块
        latest_stats_block = self._extract_latest_stats_block(lines, protocol_name)

        return latest_stats_block

    def _extract_latest_stats_block(self, lines: list[str], protocol_name: str) -> str:
        """从日志行中提取最新的完整统计数据块

        Args:
            lines: 日志行列表
            protocol_name: 协议名称

        Returns:
            最新的完整统计数据块
        """
        # 查找所有stats表头的位置
        stats_header_positions = []
        qps_header_positions = []

        for i, line in enumerate(lines):
            # 查找协议stats表头
            if f"stats {protocol_name}" in line and ("total" in line and "success" in line and "failure" in line):
                stats_header_positions.append(i)
            # 查找qps stats表头
            elif "stats qps" in line and ("all" in line and "2s" in line and "1m" in line and "1h" in line):
                qps_header_positions.append(i)

        if not stats_header_positions:
            # 如果没有找到完整的stats表头，返回所有相关行
            relevant_lines = [line for line in lines if protocol_name in line or "stats" in line.lower()]
            return '\n'.join(relevant_lines)

        # 使用最后一个stats表头位置
        last_stats_pos = stats_header_positions[-1]

        # 查找最接近的qps表头位置（在stats表头之后）
        qps_pos = None
        for pos in qps_header_positions:
            if pos > last_stats_pos:
                qps_pos = pos
                break

        # 构建最新的统计数据块
        result_lines = []

        # 添加stats表头
        if last_stats_pos < len(lines):
            result_lines.append(lines[last_stats_pos])

        # 添加协议统计数据（从stats表头后开始，直到遇到qps表头或其他非相关行）
        current_pos = last_stats_pos + 1
        while current_pos < len(lines):
            line = lines[current_pos]

            # 如果遇到qps表头，停止添加协议统计数据
            if qps_pos and current_pos >= qps_pos:
                break

            # 如果是协议相关的统计行，添加它
            if (protocol_name in line and
                any(keyword in line for keyword in ["session:", "parser:", "match:", "request", "response"])):
                result_lines.append(line)
            # 如果遇到下一个stats表头，停止
            elif "stats " in line and protocol_name in line and "total" in line:
                break

            current_pos += 1

        # 添加qps统计数据
        if qps_pos:
            # 添加qps表头
            result_lines.append("")  # 空行分隔
            result_lines.append(lines[qps_pos])

            # 添加协议相关的qps数据
            current_pos = qps_pos + 1
            while current_pos < len(lines):
                line = lines[current_pos]

                # 如果是协议相关的qps行，添加它
                if protocol_name in line and "qps:" in line:
                    result_lines.append(line)
                # 如果遇到其他统计数据或空行过多，停止
                elif ("stats " in line and protocol_name not in line) or current_pos > qps_pos + 20:
                    break

                current_pos += 1

        # 如果没有找到有效数据，返回原始内容的相关部分
        if len(result_lines) <= 1:
            relevant_lines = [line for line in lines if protocol_name in line]
            return '\n'.join(relevant_lines) if relevant_lines else "未找到相关统计数据"

        return '\n'.join(result_lines)

    async def transfer_file_between_servers(
        self,
        source_host: str,
        source_user: Optional[str],
        source_path: str,
        dest_host: str,
        dest_user: Optional[str],
        dest_path: str,
        source_password: Optional[str] = None,
        dest_password: Optional[str] = None
    ) -> bool:
        """在两个服务器之间传输文件

        Args:
            source_host: 源服务器IP
            source_user: 源服务器SSH用户名
            source_path: 源文件路径
            dest_host: 目标服务器IP
            dest_user: 目标服务器SSH用户名
            dest_path: 目标文件路径
            source_password: 源服务器SSH密码（可选）
            dest_password: 目标服务器SSH密码（可选）

        Returns:
            是否成功
        """
        try:
            # 自动获取凭据
            final_source_user, final_source_password = await self._get_credentials_for_host(source_host, source_user, source_password)
            final_dest_user, final_dest_password = await self._get_credentials_for_host(dest_host, dest_user, dest_password)

            logger.info(f"服务器间文件传输: {final_source_user}@{source_host}:{source_path} -> {final_dest_user}@{dest_host}:{dest_path}")

            # 方法1: 如果两个服务器相同，直接复制
            if source_host == dest_host and final_source_user == final_dest_user:
                # 检查目标路径类型
                dest_path_type = await self.get_path_type(dest_host, final_dest_user, dest_path)
                actual_dest_path = dest_path

                if dest_path_type == "directory":
                    # 如果目标是目录，将文件名添加到路径中
                    source_filename = source_path.split('/')[-1]
                    actual_dest_path = f"{dest_path.rstrip('/')}/{source_filename}"
                    logger.info(f"同服务器复制：目标是目录，调整目标路径为: {actual_dest_path}")

                command = f"cp {source_path} {actual_dest_path}"
                stdout, stderr, return_code = await self.execute_command(
                    source_host, final_source_user, command, timeout=300, password=final_source_password
                )
                if return_code == 0:
                    logger.info("同服务器文件复制成功")
                    return True
                else:
                    logger.error(f"同服务器文件复制失败: {stderr}")
                    return False

            # 方法2: 使用scp命令进行服务器间传输
            # 首先检查源文件是否存在
            if not await self.file_exists(source_host, final_source_user, source_path):
                logger.error(f"源文件不存在: {source_path}")
                return False

            # 检查目标路径类型，如果是目录则调整目标路径
            dest_path_type = await self.get_path_type(dest_host, final_dest_user, dest_path)
            actual_dest_path = dest_path

            if dest_path_type == "directory":
                # 如果目标是目录，将文件名添加到路径中
                source_filename = source_path.split('/')[-1]
                actual_dest_path = f"{dest_path.rstrip('/')}/{source_filename}"
                logger.info(f"目标是目录，调整目标路径为: {actual_dest_path}")

            # 构建scp命令，从源服务器传输到目标服务器
            # 注意：这需要源服务器能够SSH到目标服务器
            scp_command = f"scp -o StrictHostKeyChecking=no {source_path} {final_dest_user}@{dest_host}:{actual_dest_path}"

            # 如果目标服务器需要密码，我们需要使用sshpass
            if final_dest_password:
                scp_command = f"sshpass -p '{final_dest_password}' {scp_command}"

            stdout, stderr, return_code = await self.execute_command(
                source_host, final_source_user, scp_command, timeout=600, password=final_source_password
            )

            if return_code == 0:
                logger.info("服务器间文件传输成功")
                # 验证实际目标路径是否存在
                final_path_type = await self.get_path_type(dest_host, final_dest_user, actual_dest_path)
                if final_path_type in ['file', 'directory']:
                    logger.info(f"目标文件验证成功，路径: {actual_dest_path}, 类型: {final_path_type}")
                    return True
                else:
                    logger.error(f"目标文件验证失败，路径: {actual_dest_path}, 类型: {final_path_type}")
                    return False
            else:
                logger.error(f"服务器间文件传输失败: {stderr}")

                # 方法3: 如果scp失败，尝试通过本地中转
                logger.info("尝试通过本地中转传输文件")
                return await self._transfer_via_local(
                    source_host, final_source_user, source_path, final_source_password,
                    dest_host, final_dest_user, dest_path, final_dest_password
                )

        except Exception as e:
            logger.error(f"服务器间文件传输异常: {e}")
            return False

    async def _transfer_via_local(
        self,
        source_host: str,
        source_user: Optional[str],
        source_path: str,
        source_password: Optional[str],
        dest_host: str,
        dest_user: Optional[str],
        dest_path: str,
        dest_password: Optional[str]
    ) -> bool:
        """通过本地中转传输文件

        Args:
            source_host: 源服务器IP
            source_user: 源服务器SSH用户名
            source_path: 源文件路径
            source_password: 源服务器SSH密码
            dest_host: 目标服务器IP
            dest_user: 目标服务器SSH用户名
            dest_path: 目标文件路径
            dest_password: 目标服务器SSH密码

        Returns:
            是否成功
        """
        import tempfile
        import os

        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                temp_path = temp_file.name

            logger.info(f"使用临时文件: {temp_path}")

            # 步骤1: 从源服务器下载到本地
            success = await self.copy_file_from_remote(
                source_host, source_user, source_path, temp_path, source_password
            )

            if not success:
                logger.error("从源服务器下载文件失败")
                os.unlink(temp_path)
                return False

            # 步骤2: 从本地上传到目标服务器
            success = await self.copy_file_to_remote(
                dest_host, dest_user, temp_path, dest_path, dest_password
            )

            # 清理临时文件
            os.unlink(temp_path)

            if success:
                logger.info("通过本地中转传输成功")
                return True
            else:
                logger.error("上传到目标服务器失败")
                return False

        except Exception as e:
            logger.error(f"本地中转传输异常: {e}")
            return False